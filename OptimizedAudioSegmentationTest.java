public class OptimizedAudioSegmentationTest {
    
    public static void main(String[] args) {
        // Test different durations
        double[] testDurations = {0.3, 0.5, 1.0, 2.5, 5.0, 10.01, 15.7, 30.0, 60.5};
        
        System.out.println("Testing optimized audio segmentation algorithm");
        System.out.println("=".repeat(80));
        
        long totalTime = 0;
        
        for (double duration : testDurations) {
            long startTime = System.nanoTime();
            double segmentDuration = calculateAudioSegmentation(duration);
            long endTime = System.nanoTime();
            
            long executionTime = endTime - startTime;
            totalTime += executionTime;
            
            // Verify the result
            String verification = verifySegmentation(duration, segmentDuration);
            
            System.out.printf("Duration: %6.2fs -> Segment: %6.3fs [%s] (Time: %d ns)%n", 
                    duration, segmentDuration, verification, executionTime);
        }
        
        System.out.println("=".repeat(80));
        System.out.printf("Total execution time: %d ns (%.3f ms)%n", totalTime, totalTime / 1_000_000.0);
        System.out.printf("Average execution time: %d ns%n", totalTime / testDurations.length);
    }
    
    private static double calculateAudioSegmentation(double totalDurationSeconds) {
        if (totalDurationSeconds <= 0.4) {
            return totalDurationSeconds;
        }

        // Try preferred durations (from large to small) to find the first suitable one
        double[] preferredDurations = {5.0, 4.0, 3.0, 2.5, 2.0, 1.5, 1.0, 0.8, 0.6, 0.5, 0.4};

        for (double segmentDuration : preferredDurations) {
            if (segmentDuration > totalDurationSeconds) {
                continue; // Segment duration cannot exceed total duration
            }

            int fullSegments = (int) Math.floor(totalDurationSeconds / segmentDuration);
            double remainder = totalDurationSeconds - (fullSegments * segmentDuration);

            // If divisible (remainder ~= 0) or remainder >= 0.4s, this is suitable
            if (remainder < 0.001 || remainder >= 0.4) {
                return segmentDuration;
            }
        }

        // If no preferred duration works, calculate optimal segment duration
        double optimalSegmentDuration = (totalDurationSeconds - 0.4) / Math.floor((totalDurationSeconds - 0.4) / 0.4);
        return optimalSegmentDuration;
    }
    
    private static String verifySegmentation(double totalDuration, double segmentDuration) {
        if (totalDuration <= 0.4) {
            return "OK (No split needed)";
        }
        
        int fullSegments = (int) Math.floor(totalDuration / segmentDuration);
        double lastSegmentDuration = totalDuration - (fullSegments * segmentDuration);
        
        if (Math.abs(lastSegmentDuration) < 0.001) {
            return String.format("OK (Perfect split: %d segments)", fullSegments);
        } else if (lastSegmentDuration >= 0.4) {
            return String.format("OK (%d + %.3fs)", fullSegments, lastSegmentDuration);
        } else {
            return String.format("FAIL (Last segment: %.3fs < 0.4s)", lastSegmentDuration);
        }
    }
}
