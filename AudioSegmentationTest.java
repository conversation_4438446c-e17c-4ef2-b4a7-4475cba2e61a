public class AudioSegmentationTest {

    /**
     * Test audio segmentation logic
     */
    public static void main(String[] args) {
        double totalDuration = 10.01;

        System.out.println("Testing audio segmentation logic, total duration: " + totalDuration + " seconds");
        System.out.println("============================================================");

        // Test different segment durations
        double[] testSegmentDurations = {1.0, 2.0, 3.0, 4.0, 5.0};

        for (double segmentDuration : testSegmentDurations) {
            testSegmentation(totalDuration, segmentDuration);
        }

        // Test our algorithm
        System.out.println("\nUsing algorithm to calculate best segmentation:");
        double bestSegmentDuration = calculateAudioSegmentation(totalDuration);
        System.out.println("Best segment duration: " + bestSegmentDuration + " seconds");
        testSegmentation(totalDuration, bestSegmentDuration);
    }
    
    private static void testSegmentation(double totalDuration, double segmentDuration) {
        int fullSegments = (int) Math.floor(totalDuration / segmentDuration);
        double lastSegmentDuration = totalDuration - (fullSegments * segmentDuration);
        int totalSegments = lastSegmentDuration == 0 ? fullSegments : fullSegments + 1;

        boolean isValid = lastSegmentDuration == 0 || lastSegmentDuration >= 0.4;
        String status = isValid ? "OK" : "FAIL";

        System.out.printf("Segment duration: %.1fs -> Full segments: %d, Last segment: %.3fs, Total segments: %d [%s]%n",
                segmentDuration, fullSegments, lastSegmentDuration, totalSegments, status);
    }
    
    private static double calculateAudioSegmentation(double totalDurationSeconds) {
        if (totalDurationSeconds <= 0.4) {
            return totalDurationSeconds;
        }

        for (double segmentDuration = 1.0; segmentDuration <= totalDurationSeconds; segmentDuration += 0.1) {
            int fullSegments = (int) Math.floor(totalDurationSeconds / segmentDuration);
            double lastSegmentDuration = totalDurationSeconds - (fullSegments * segmentDuration);
            
            if (lastSegmentDuration == 0 || lastSegmentDuration >= 0.4) {
                return segmentDuration;
            }
        }
        
        return totalDurationSeconds;
    }
}
