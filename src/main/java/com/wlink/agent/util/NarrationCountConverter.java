package com.wlink.agent.util;

import com.wlink.agent.model.dto.NarrationCountDto;
import com.wlink.agent.model.res.NarrationCountRes;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 旁白统计数据转换工具类
 */
public class NarrationCountConverter {

    /**
     * 将DTO转换为响应对象
     */
    public static NarrationCountRes toResponse(NarrationCountDto dto) {
        if (dto == null) {
            return null;
        }

        NarrationCountRes response = new NarrationCountRes();
        response.setSessionId(dto.getSessionId());
        response.setTotalNarrationCount(dto.getTotalNarrationCount());
        response.setTotalCharacterCount(dto.getTotalCharacterCount());

        if (dto.getChapters() != null) {
            List<NarrationCountRes.ChapterStatInfo> chapters = dto.getChapters().stream()
                    .map(NarrationCountConverter::convertChapter)
                    .collect(Collectors.toList());
            response.setChapters(chapters);
        }

        return response;
    }

    /**
     * 将响应对象转换为DTO（注意：响应对象中没有隐藏字段，此方法主要用于测试）
     */
    public static NarrationCountDto toDto(NarrationCountRes response) {
        if (response == null) {
            return null;
        }

        NarrationCountDto dto = new NarrationCountDto();
        dto.setSessionId(response.getSessionId());
        dto.setTotalNarrationCount(response.getTotalNarrationCount());
        dto.setTotalCharacterCount(response.getTotalCharacterCount());

        // 响应对象中没有characterSummary字段，设置为空列表
        dto.setCharacterSummary(new ArrayList<>());

        if (response.getChapters() != null) {
            List<NarrationCountDto.ChapterStatInfo> chapters = response.getChapters().stream()
                    .map(NarrationCountConverter::convertChapterToDto)
                    .collect(Collectors.toList());
            dto.setChapters(chapters);
        }

        return dto;
    }

    private static NarrationCountRes.ChapterStatInfo convertChapter(NarrationCountDto.ChapterStatInfo dtoChapter) {
        NarrationCountRes.ChapterStatInfo resChapter = new NarrationCountRes.ChapterStatInfo();
        resChapter.setSegmentId(dtoChapter.getSegmentId());
        resChapter.setSegmentName(dtoChapter.getSegmentName());
        resChapter.setNarrationCount(dtoChapter.getNarrationCount());
        resChapter.setCharacterCount(dtoChapter.getCharacterCount());

        if (dtoChapter.getCharacterGroups() != null) {
            List<NarrationCountRes.CharacterGroup> groups = dtoChapter.getCharacterGroups().stream()
                    .map(NarrationCountConverter::convertCharacterGroup)
                    .collect(Collectors.toList());
            resChapter.setCharacterGroups(groups);
        }

        return resChapter;
    }

    private static NarrationCountDto.ChapterStatInfo convertChapterToDto(NarrationCountRes.ChapterStatInfo resChapter) {
        NarrationCountDto.ChapterStatInfo dtoChapter = new NarrationCountDto.ChapterStatInfo();
        dtoChapter.setSegmentId(resChapter.getSegmentId());
        dtoChapter.setSegmentName(resChapter.getSegmentName());
        dtoChapter.setNarrationCount(resChapter.getNarrationCount());
        dtoChapter.setCharacterCount(resChapter.getCharacterCount());

        // 响应对象中没有隐藏字段，设置为空列表
        dtoChapter.setNarrationDetails(new ArrayList<>());
        dtoChapter.setCharacterDetails(new ArrayList<>());

        if (resChapter.getCharacterGroups() != null) {
            List<NarrationCountDto.CharacterGroup> groups = resChapter.getCharacterGroups().stream()
                    .map(NarrationCountConverter::convertCharacterGroupToDto)
                    .collect(Collectors.toList());
            dtoChapter.setCharacterGroups(groups);
        }

        return dtoChapter;
    }

    private static NarrationCountRes.CharacterGroup convertCharacterGroup(NarrationCountDto.CharacterGroup dtoGroup) {
        NarrationCountRes.CharacterGroup resGroup = new NarrationCountRes.CharacterGroup();
        resGroup.setCharacterId(dtoGroup.getCharacterId());
        resGroup.setCharacterName(dtoGroup.getCharacterName());
        resGroup.setCount(dtoGroup.getCount());
        return resGroup;
    }

    private static NarrationCountDto.CharacterGroup convertCharacterGroupToDto(NarrationCountRes.CharacterGroup resGroup) {
        NarrationCountDto.CharacterGroup dtoGroup = new NarrationCountDto.CharacterGroup();
        dtoGroup.setCharacterId(resGroup.getCharacterId());
        dtoGroup.setCharacterName(resGroup.getCharacterName());
        dtoGroup.setCount(resGroup.getCount());
        // 响应对象中没有details字段，设置为空列表
        dtoGroup.setDetails(new ArrayList<>());
        return dtoGroup;
    }
}
