package com.wlink.agent.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 修改分镜旁白请求
 */
@Data
@Schema(description = "修改分镜旁白请求")
public class UpdateShotNarrationReq {

    @NotNull(message = "分镜ID不能为空")
    @Schema(description = "分镜ID", required = true, example = "123")
    @JsonProperty("shotId")
    private Long shotId;

    @NotBlank(message = "旁白文本不能为空")
    @Schema(description = "旁白文本", required = true, example = "这是新的旁白内容")
    @JsonProperty("narrationText")
    private String narrationText;

    @NotNull(message = "索引不能为空")
    @Schema(description = "旁白在line_list中的索引", required = true, example = "1")
    @JsonProperty("index")
    private Integer index;
}
