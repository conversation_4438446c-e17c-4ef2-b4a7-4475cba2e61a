package com.wlink.agent.model.dto;

import lombok.Data;

import java.util.List;

/**
 * 旁白统计数据传输对象 - 用于Redis存储
 */
@Data
public class NarrationCountDto {

    private String sessionId;
    private List<ChapterStatInfo> chapters;
    private Integer totalNarrationCount;
    private Integer totalCharacterCount;
    private List<CharacterSummary> characterSummary;

    /**
     * 章节统计信息
     */
    @Data
    public static class ChapterStatInfo {
        private String segmentId;
        private String segmentName;
        private Integer narrationCount;
        private List<ContentDetail> narrationDetails;
        private Integer characterCount;
        private List<ContentDetail> characterDetails;
        private List<CharacterGroup> characterGroups;
    }

    /**
     * 内容详情信息
     */
    @Data
    public static class ContentDetail {
        private String shotId;
        private Integer id;
        private String content;
        private String characterId;
        private String characterName;
    }

    /**
     * 角色分组信息
     */
    @Data
    public static class CharacterGroup {
        private String characterId;
        private String characterName;
        private Integer count;
        private List<ContentDetail> details;
    }

    /**
     * 角色统计汇总
     */
    @Data
    public static class CharacterSummary {
        private String characterId;
        private String characterName;
        private Integer totalCount;
    }
}
