package com.wlink.agent.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 修改分镜旁白响应
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "修改分镜旁白响应")
public class UpdateShotNarrationRes {

    @Schema(description = "分镜ID", example = "123")
    @JsonProperty("shotId")
    private Long shotId;

    @Schema(description = "更新的旁白文本", example = "这是新的旁白内容")
    @JsonProperty("narrationText")
    private String narrationText;

    @Schema(description = "生成的音频URL", example = "/data/audio/narration_20240729103000_abcdef12.wav")
    @JsonProperty("audioUrl")
    private String audioUrl;

    @Schema(description = "音频时长（毫秒）", example = "5000")
    @JsonProperty("duration")
    private Long duration;

    @Schema(description = "TTS记录ID", example = "100")
    @JsonProperty("recordId")
    private Long recordId;
}
