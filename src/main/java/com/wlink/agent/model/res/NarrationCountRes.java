package com.wlink.agent.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 旁白统计响应
 */
@Data
@Schema(description = "旁白统计响应")
public class NarrationCountRes {

    @Schema(description = "会话ID")
    @JsonProperty("sessionId")
    private String sessionId;

    @Schema(description = "章节统计列表")
    @JsonProperty("chapters")
    private List<ChapterStatInfo> chapters;

    @Schema(description = "总旁白数量")
    @JsonProperty("totalNarrationCount")
    private Integer totalNarrationCount;

    @Schema(description = "总角色音频数量")
    @JsonProperty("totalCharacterCount")
    private Integer totalCharacterCount;

    /**
     * 章节统计信息
     */
    @Data
    @Schema(description = "章节统计信息")
    public static class ChapterStatInfo {

        @Schema(description = "章节ID")
        @JsonProperty("segmentId")
        private String segmentId;

        @Schema(description = "章节名称")
        @JsonProperty("segmentName")
        private String segmentName;

        @Schema(description = "旁白数量")
        @JsonProperty("narrationCount")
        private Integer narrationCount;

        @Schema(description = "角色音频数量")
        @JsonProperty("characterCount")
        private Integer characterCount;

        @Schema(description = "角色统计分组")
        @JsonProperty("characterGroups")
        private List<CharacterGroup> characterGroups;
    }



    /**
     * 角色分组信息
     */
    @Data
    @Schema(description = "角色分组信息")
    public static class CharacterGroup {

        @Schema(description = "角色ID")
        @JsonProperty("characterId")
        private String characterId;

        @Schema(description = "角色名称")
        @JsonProperty("characterName")
        private String characterName;

        @Schema(description = "该角色在此章节的音频数量")
        @JsonProperty("count")
        private Integer count;
    }
}
