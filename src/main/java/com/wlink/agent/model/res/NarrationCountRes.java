package com.wlink.agent.model.res;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 旁白统计响应
 */
@Data
@Schema(description = "旁白统计响应")
public class NarrationCountRes {

    @Schema(description = "会话ID")
    @JsonProperty("sessionId")
    private String sessionId;

    @Schema(description = "章节统计列表")
    @JsonProperty("chapters")
    private List<ChapterStatInfo> chapters;

    @Schema(description = "总旁白数量")
    @JsonProperty("totalNarrationCount")
    private Integer totalNarrationCount;

    @Schema(description = "总角色音频数量")
    @JsonProperty("totalCharacterCount")
    private Integer totalCharacterCount;

    @Schema(description = "角色统计汇总")
    @JsonProperty("characterSummary")
    @JsonIgnore
    private List<CharacterSummary> characterSummary;

    /**
     * 章节统计信息
     */
    @Data
    @Schema(description = "章节统计信息")
    public static class ChapterStatInfo {

        @Schema(description = "章节ID")
        @JsonProperty("segmentId")
        private String segmentId;

        @Schema(description = "章节名称")
        @JsonProperty("segmentName")
        private String segmentName;

        @Schema(description = "旁白数量")
        @JsonProperty("narrationCount")
        private Integer narrationCount;

        @Schema(description = "旁白详情列表")
        @JsonProperty("narrationDetails")
        @JsonIgnore
        private List<ContentDetail> narrationDetails;

        @Schema(description = "角色音频数量")
        @JsonProperty("characterCount")
        private Integer characterCount;

        @Schema(description = "角色音频详情列表")
        @JsonProperty("characterDetails")
        @JsonIgnore
        private List<ContentDetail> characterDetails;

        @Schema(description = "角色统计分组")
        @JsonProperty("characterGroups")
        private List<CharacterGroup> characterGroups;
    }

    /**
     * 内容详情信息
     */
    @Data
    @Schema(description = "内容详情信息")
    public static class ContentDetail {

        @Schema(description = "分镜ID")
        @JsonProperty("shotId")
        private String shotId;

        @Schema(description = "ID")
        @JsonProperty("id")
        private Integer id;

        @Schema(description = "内容")
        @JsonProperty("content")
        private String content;

        @Schema(description = "角色ID（仅角色音频时有值）")
        @JsonProperty("characterId")
        private String characterId;

        @Schema(description = "角色名称（仅角色音频时有值）")
        @JsonProperty("characterName")
        private String characterName;
    }

    /**
     * 角色分组信息
     */
    @Data
    @Schema(description = "角色分组信息")
    public static class CharacterGroup {

        @Schema(description = "角色ID")
        @JsonProperty("characterId")
        private String characterId;

        @Schema(description = "角色名称")
        @JsonProperty("characterName")
        private String characterName;

        @Schema(description = "该角色在此章节的音频数量")
        @JsonProperty("count")
        private Integer count;

        @Schema(description = "该角色的音频详情")
        @JsonProperty("details")
        private List<ContentDetail> details;
    }

    /**
     * 角色统计汇总
     */
    @Data
    @Schema(description = "角色统计汇总")
    public static class CharacterSummary {

        @Schema(description = "角色ID")
        @JsonProperty("characterId")
        private String characterId;

        @Schema(description = "角色名称")
        @JsonProperty("characterName")
        private String characterName;

        @Schema(description = "该角色的总音频数量")
        @JsonProperty("totalCount")
        private Integer totalCount;
    }
}
