package com.wlink.agent.service;

import com.wlink.agent.model.req.ShotLipSyncReq;
import com.wlink.agent.model.res.ShotLipSyncRes;

import java.util.List;

/**
 * 分镜对口型服务接口
 */
public interface ShotLipSyncService {

    /**
     * 提交分镜对口型任务
     *
     * @param req 对口型请求
     * @return 对口型响应
     */
    ShotLipSyncRes submitLipSync(ShotLipSyncReq req);

    /**
     * 根据任务ID查询对口型记录
     *
     * @param taskId 任务ID
     * @return 对口型记录
     */
    ShotLipSyncRes getLipSyncRecordByTaskId(String taskId);

    /**
     * 根据分镜ID查询对口型记录列表
     *
     * @param shotId 分镜ID
     * @return 对口型记录列表
     */
    List<ShotLipSyncRes> getLipSyncRecordsByShotId(Long shotId);

    /**
     * 更新对口型记录状态
     *
     * @param taskId 任务ID
     * @param status 状态
     * @param resultVideoUrl 结果视频URL
     * @param taskCostTime 任务耗时
     */
    void updateLipSyncRecordStatus(String taskId, String status, String resultVideoUrl, Long taskCostTime);

    /**
     * 更新对口型记录为失败状态
     *
     * @param taskId 任务ID
     * @param errorMessage 错误信息
     */
    void updateLipSyncRecordToFailed(String taskId, String errorMessage);

    /**
     * 音频合成完成后开始对口型处理
     *
     * @param shotId 分镜ID
     * @param finalAudioUrl 合成后的音频URL
     */
    void startLipSyncAfterAudioSynthesis(Long shotId, String finalAudioUrl,String userId);

    /**
     * 处理对口型回调结果
     *
     * @param taskId 任务ID
     * @param eventData 回调事件数据
     */
    void handleLipSyncCallback(String taskId, Object eventData);
}
