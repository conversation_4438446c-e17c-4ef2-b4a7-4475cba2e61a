package com.wlink.agent.service.impl;

import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.wlink.agent.client.ComfyUIApiClient;
import com.wlink.agent.client.model.comfyui.ComfyUINodeInfo;
import com.wlink.agent.client.model.comfyui.ComfyUIRunRequest;
import com.wlink.agent.client.model.comfyui.ComfyUIRunResponse;
import com.wlink.agent.dao.mapper.AiCanvasAudioMapper;
import com.wlink.agent.dao.mapper.AiCanvasImageMapper;
import com.wlink.agent.dao.mapper.AiCanvasMaterialMapper;
import com.wlink.agent.dao.mapper.AiShotLipSyncMapper;
import com.wlink.agent.dao.po.AiCanvasAudioPo;
import com.wlink.agent.dao.po.AiCanvasImagePo;
import com.wlink.agent.dao.po.AiCanvasMaterialPo;
import com.wlink.agent.dao.po.AiCanvasShotPo;
import com.wlink.agent.dao.po.AiShotLipSyncPo;
import com.wlink.agent.enums.ShotStatus;
import com.wlink.agent.enums.TransactionTypeEnum;
import com.wlink.agent.model.dto.SimpleUserInfo;
import com.wlink.agent.model.req.ShotLipSyncReq;
import com.wlink.agent.model.res.ShotLipSyncRes;
import com.wlink.agent.service.AiCanvasShotService;
import com.wlink.agent.service.AudioMergeService;
import com.wlink.agent.service.AudioSynthesisService;
import com.wlink.agent.service.ShotLipSyncService;
import com.wlink.agent.service.UserPointsService;
import com.wlink.agent.dao.mapper.AiCanvasVideoMapper;
import com.wlink.agent.dao.po.AiCanvasVideoPo;
import com.wlink.agent.utils.OssUtils;
import com.wlink.agent.utils.MediaUrlPrefixUtil;
import com.wlink.agent.utils.UserContext;
import com.wlink.agent.model.dto.AudioMergeResult;
import com.wlink.agent.model.dto.AudioMergeRequest;
import com.wlink.agent.model.dto.AudioSynthesisRequest;
import com.wlink.agent.model.dto.AudioSynthesisResult;
import com.wlink.agent.client.model.comfyui.ComfyUICallbackEventData;
import cn.hutool.core.util.IdUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 分镜对口型服务实现
 */
@Slf4j
@Service
@RefreshScope
@RequiredArgsConstructor
public class ShotLipSyncServiceImpl implements ShotLipSyncService {

    private final AiShotLipSyncMapper shotLipSyncMapper;
    private final AiCanvasShotService aiCanvasShotService;
    private final AiCanvasAudioMapper canvasAudioMapper;
    private final AiCanvasImageMapper canvasImageMapper;
    private final ComfyUIApiClient comfyUIApiClient;
    private final AiCanvasMaterialMapper aiCanvasMaterialMapper;

    // 会自动注入@Primary的AudioMergeService实现（SimpleAudioMergeServiceImpl）
    private final AudioMergeService audioMergeService;

    // 音频合成服务
    private final AudioSynthesisService audioSynthesisService;

    // 画布视频Mapper
    private final AiCanvasVideoMapper aiCanvasVideoMapper;

    // OSS工具类
    private final OssUtils ossUtils;

    // 用户积分服务
    private final UserPointsService userPointsService;

    @Value("${comfyui.lip-sync.webapp-id:1950119681346723842}")
    private Long webappId;

    @Value("${spring.profiles.active}")
    private String env;

    @Value("${comfyui.lip-sync.api-key:264fec3cd17144c59ec690b37a016972}")
    private String apiKey;

    @Value("${comfyui.webhook-url:https://dev.neodomain.cn/agent/comfyui/callback}")
    private String webhookUrl;

    /**
     * OSS路径模板
     */
    private static final String OSS_VIDEO_PATH = "dify/{env}/{userId}/{type}/";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShotLipSyncRes submitLipSync(ShotLipSyncReq req) {
        log.info("提交分镜对口型任务: shotId={}", req.getShotId());

        // 1. 验证分镜是否存在
        AiCanvasShotPo aiCanvasShotPo = aiCanvasShotService.getById(req.getShotId());
        if (aiCanvasShotPo == null) {
            log.error("分镜不存在: shotId={}", req.getShotId());
            throw new BizException("分镜不存在");
        }

        // 2. 获取当前用户信息
        SimpleUserInfo userInfo = UserContext.getUser();
        String userId = userInfo != null ? userInfo.getUserId() : "";

        // 3. 查询分镜下的所有音频数据
        List<AiCanvasAudioPo> audioList = getAudioListByShot(aiCanvasShotPo);
        if (audioList.isEmpty()) {
            log.error("分镜下没有音频数据: shotId={}", req.getShotId());
            throw new BizException("分镜下没有音频数据");
        }

        // 4. 查询分镜的图片数据
        AiCanvasImagePo imagePo = getImageByShot(aiCanvasShotPo);
        if (imagePo == null) {
            log.error("分镜下没有图片数据: shotId={}", req.getShotId());
            throw new BizException("分镜下没有图片数据");
        }

        // 5. 处理音频合成（如果有多个音频）
        AudioSynthesisResult audioSynthesisResult = processFinalAudio(audioList, req.getShotId(), userId);

        // 6. 更新分镜状态为处理中
        aiCanvasShotPo.setShotStatus(1); // 处理中
        aiCanvasShotPo.setUpdateTime(new Date());
        aiCanvasShotService.updateById(aiCanvasShotPo);

        // 7. 构建响应
        ShotLipSyncRes response = new ShotLipSyncRes();
        response.setShotId(req.getShotId());
        response.setTaskId(audioSynthesisResult.getTaskId());
        response.setUserId(userId);
        response.setCreateTime(new Date());
        response.setUpdateTime(new Date());

        if ("DIRECT_LIP_SYNC".equals(audioSynthesisResult.getStatus())) {
            // 单个音频，直接进行对口型处理
            response.setStatus("RUNNING"); // 对口型处理阶段
            log.info("分镜直接对口型任务已启动: shotId={}, taskId={}",
                    req.getShotId(), audioSynthesisResult.getTaskId());
        } else {
            // 多个音频，先进行音频合成
            response.setStatus("AUDIO_SYNTHESIS"); // 音频合成阶段
            log.info("分镜音频合成任务提交成功: shotId={}, audioTaskId={}",
                    req.getShotId(), audioSynthesisResult.getTaskId());
        }

        return response;
    }

    /**
     * 获取分镜的音频列表
     */
    private List<AiCanvasAudioPo> getAudioListByShot(AiCanvasShotPo shot) {
        LambdaQueryWrapper<AiCanvasAudioPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiCanvasAudioPo::getCanvasId, shot.getCanvasId())
                .eq(AiCanvasAudioPo::getShotCode, shot.getCode())
                .eq(AiCanvasAudioPo::getDelFlag, 0)
                .orderByAsc(AiCanvasAudioPo::getSortOrder);
        return canvasAudioMapper.selectList(queryWrapper);
    }

    /**
     * 获取分镜的图片数据
     */
    private AiCanvasImagePo getImageByShot(AiCanvasShotPo shot) {
        LambdaQueryWrapper<AiCanvasImagePo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiCanvasImagePo::getCanvasId, shot.getCanvasId())
                .eq(AiCanvasImagePo::getShotCode, shot.getCode())
                .eq(AiCanvasImagePo::getDelFlag, 0)
                .last("limit 1");
        return canvasImageMapper.selectOne(queryWrapper);
    }

    /**
     * 处理最终音频（合成多个音频或直接使用单个音频）
     * 如果只有一个音频，直接触发对口型处理
     * 如果有多个音频，先进行音频合成
     * 如果所有音频的audioType都是2，不支持对口型
     */
    private AudioSynthesisResult processFinalAudio(List<AiCanvasAudioPo> audioList, Long shotId, String userId) {
        // 检查音频类型，如果所有音频的audioType都是2，则不支持对口型
        boolean allAudioType2 = audioList.stream()
                .allMatch(audio -> audio.getAudioType() != null && audio.getAudioType() == 2);

        if (allAudioType2) {
            log.warn("分镜所有音频类型都是2，不支持对口型: shotId={}", shotId);
            throw new BizException("此分镜不支持对口型");
        }

        List<String> audioUrls = audioList.stream()
                .map(AiCanvasAudioPo::getAudioUrl)
                .collect(Collectors.toList());




        if (audioList.size() == 1) {
            // 只有一个音频，直接触发对口型处理
            String singleAudioUrl = audioUrls.get(0);
            AiCanvasAudioPo singleAudio = audioList.get(0);
            log.info("分镜只有1个音频，直接触发对口型处理: shotId={}, audioUrl={}, audioType={}",
                    shotId, singleAudioUrl, singleAudio.getAudioType());

            try {
                // 直接开始对口型处理
                startLipSyncAfterAudioSynthesis(shotId, singleAudioUrl, userId);
                // 返回一个模拟的成功结果
                AudioSynthesisResult result = new AudioSynthesisResult();
                result.setTaskId("direct_" + shotId); // 使用特殊的taskId标识直接处理
                result.setShotId(shotId);
                result.setSuccess(true);
                result.setStatus("DIRECT_LIP_SYNC"); // 直接对口型状态
                result.setResultAudioUrl(singleAudioUrl);
                return result;
            } catch (Exception e) {
                log.error("直接对口型处理失败: shotId={}, audioUrl={}", shotId, singleAudioUrl, e);
                throw new BizException("对口型处理失败: " + e.getMessage());
            }
        } else {
            // 多个音频需要合成
            log.info("分镜有{}个音频，需要合成: shotId={}", audioList.size(), shotId);

            // 记录音频类型信息
            audioList.forEach(audio ->
                log.debug("音频信息: shotId={}, audioUrl={}, audioType={}",
                        shotId, audio.getAudioUrl(), audio.getAudioType())
            );

            try {
                // 使用音频合成服务
                AudioSynthesisRequest request = new AudioSynthesisRequest();
                request.setShotId(shotId);
                request.setUserId(userId);
                request.setAudioUrls(audioUrls);

                AudioSynthesisResult result = audioSynthesisService.submitSynthesisTask(request);

                log.info("音频合成任务提交成功: shotId={}, taskId={}, status={}",
                        shotId, result.getTaskId(), result.getStatus());

                return result;

            } catch (Exception e) {
                log.error("音频合成任务提交失败: shotId={}", shotId, e);
                throw new BizException("音频合成任务提交失败: " + e.getMessage());
            }
        }
    }

    /**
     * 音频合成完成后，开始对口型处理
     * 这个方法会在音频合成回调成功时被调用
     */
    @Transactional(rollbackFor = Exception.class)
    public void startLipSyncAfterAudioSynthesis(Long shotId, String finalAudioUrl,String userId) {
        log.info("音频合成完成，开始对口型处理: shotId={}, audioUrl={}", shotId, finalAudioUrl);

        try {
            // 1. 验证分镜是否存在
            AiCanvasShotPo aiCanvasShotPo = aiCanvasShotService.getById(shotId);
            if (aiCanvasShotPo == null) {
                log.error("分镜不存在: shotId={}", shotId);
                throw new BizException("分镜不存在");
            }

            // 2. 查询分镜的图片数据
            AiCanvasImagePo imagePo = getImageByShot(aiCanvasShotPo);
            if (imagePo == null) {
                log.error("分镜下没有图片数据: shotId={}", shotId);
                throw new BizException("分镜下没有图片数据");
            }


            // 4. 构建 ComfyUI 请求
            ComfyUIRunRequest comfyUIRequest = buildComfyUIRequest(finalAudioUrl, imagePo, aiCanvasShotPo);

            // 5. 调用 ComfyUI API
            ComfyUIRunResponse comfyUIResponse;
            try {
                comfyUIResponse = comfyUIApiClient.runWorkflow(comfyUIRequest);
            } catch (Exception e) {
                log.error("调用 ComfyUI API 失败: shotId={}, error={}", shotId, e.getMessage(), e);
                throw new BizException("调用对口型服务失败: " + e.getMessage());
            }

            // 6. 保存对口型记录
            AiShotLipSyncPo lipSyncRecord = new AiShotLipSyncPo();
            lipSyncRecord.setShotId(shotId);
            lipSyncRecord.setTaskId(comfyUIResponse.getData().getTaskId());
            lipSyncRecord.setClientId(comfyUIResponse.getData().getClientId());
            lipSyncRecord.setAudioUrl(finalAudioUrl);
            lipSyncRecord.setImageUrl(imagePo.getImageUrl());
            lipSyncRecord.setStatus("RUNNING");
            lipSyncRecord.setUserId(userId);
            lipSyncRecord.setCreateTime(new Date());
            lipSyncRecord.setUpdateTime(new Date());
            lipSyncRecord.setDelFlag(0);

            int insertResult = shotLipSyncMapper.insert(lipSyncRecord);
            if (insertResult <= 0) {
                log.error("保存对口型记录失败: shotId={}, taskId={}", shotId, comfyUIResponse.getData().getTaskId());
                throw new BizException("保存对口型记录失败");
            }

            log.info("对口型任务提交成功: shotId={}, taskId={}, recordId={}",
                    shotId, comfyUIResponse.getData().getTaskId(), lipSyncRecord.getId());

        } catch (Exception e) {
            log.error("对口型处理失败: shotId={}, audioUrl={}", shotId, finalAudioUrl, e);

            // 更新分镜状态为失败
            AiCanvasShotPo aiCanvasShotPo = aiCanvasShotService.getById(shotId);
            if (aiCanvasShotPo != null) {
                aiCanvasShotPo.setShotStatus(3); // 失败
                aiCanvasShotPo.setUpdateTime(new Date());
                aiCanvasShotService.updateById(aiCanvasShotPo);
            }

            throw new BizException("对口型处理失败: " + e.getMessage());
        }
    }

    /**
     * 构建 ComfyUI 请求
     */
    private ComfyUIRunRequest buildComfyUIRequest(String audioUrl, AiCanvasImagePo imagePo, AiCanvasShotPo shotPo) {
        ComfyUIRunRequest request = new ComfyUIRunRequest();
        request.setWebappId(webappId);
        request.setApiKey(apiKey);
        request.setWebhookUrl(webhookUrl);

        List<ComfyUINodeInfo> nodeInfoList = new ArrayList<>();

        // 添加文本描述节点（141）- 使用图片的videoConvertPrompt字段
        ComfyUINodeInfo textNode = new ComfyUINodeInfo();
        textNode.setNodeId("141");
        textNode.setFieldName("text");
        textNode.setFieldValue(imagePo.getVideoConvertPrompt() != null ? imagePo.getVideoConvertPrompt() : "");
        nodeInfoList.add(textNode);

        // 添加构图描述节点（133）- 使用图片的videoConvertPrompt字段
        ComfyUINodeInfo compositionNode = new ComfyUINodeInfo();
        compositionNode.setNodeId("133");
        compositionNode.setFieldName("text");
        String videoConvertPrompt = imagePo.getVideoConvertPrompt() != null ? imagePo.getVideoConvertPrompt() : "";
        compositionNode.setFieldValue(videoConvertPrompt);
        nodeInfoList.add(compositionNode);

        // 添加音频节点（174）- 使用合成后的音频
        ComfyUINodeInfo audioNode = new ComfyUINodeInfo();
        audioNode.setNodeId("174");
        audioNode.setFieldName("value");
        audioNode.setFieldValue(MediaUrlPrefixUtil.getMediaUrl(audioUrl));
        nodeInfoList.add(audioNode);

        // 添加图片节点（175）
        ComfyUINodeInfo imageNode = new ComfyUINodeInfo();
        imageNode.setNodeId("175");
        imageNode.setFieldName("value");
        imageNode.setFieldValue(MediaUrlPrefixUtil.getMediaUrl(imagePo.getImageUrl()));
        nodeInfoList.add(imageNode);

        request.setNodeInfoList(nodeInfoList);
        return request;
    }

    @Override
    public ShotLipSyncRes getLipSyncRecordByTaskId(String taskId) {
        log.info("根据任务ID查询对口型记录: taskId={}", taskId);

        LambdaQueryWrapper<AiShotLipSyncPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiShotLipSyncPo::getTaskId, taskId)
                .eq(AiShotLipSyncPo::getDelFlag, 0);

        AiShotLipSyncPo lipSyncRecord = shotLipSyncMapper.selectOne(queryWrapper);
        if (lipSyncRecord == null) {
            log.warn("未找到对口型记录: taskId={}", taskId);
            return null;
        }

        ShotLipSyncRes response = new ShotLipSyncRes();
        BeanUtils.copyProperties(lipSyncRecord, response);
        return response;
    }

    @Override
    public List<ShotLipSyncRes> getLipSyncRecordsByShotId(Long shotId) {
        log.info("根据分镜ID查询对口型记录列表: shotId={}", shotId);

        LambdaQueryWrapper<AiShotLipSyncPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiShotLipSyncPo::getShotId, shotId)
                .eq(AiShotLipSyncPo::getDelFlag, 0)
                .orderByDesc(AiShotLipSyncPo::getCreateTime);

        List<AiShotLipSyncPo> lipSyncRecords = shotLipSyncMapper.selectList(queryWrapper);

        return lipSyncRecords.stream()
                .map(record -> {
                    ShotLipSyncRes response = new ShotLipSyncRes();
                    BeanUtils.copyProperties(record, response);
                    return response;
                })
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLipSyncRecordStatus(String taskId, String status, String resultVideoUrl, Long taskCostTime) {
        log.info("更新对口型记录状态: taskId={}, status={}, resultVideoUrl={}, taskCostTime={}", 
                taskId, status, resultVideoUrl, taskCostTime);

        LambdaUpdateWrapper<AiShotLipSyncPo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(AiShotLipSyncPo::getTaskId, taskId)
                .set(AiShotLipSyncPo::getStatus, status)
                .set(AiShotLipSyncPo::getUpdateTime, new Date());

        if (resultVideoUrl != null) {
            updateWrapper.set(AiShotLipSyncPo::getResultVideoUrl, resultVideoUrl);
        }

        if (taskCostTime != null) {
            updateWrapper.set(AiShotLipSyncPo::getTaskCostTime, taskCostTime);
        }

        int updateResult = shotLipSyncMapper.update(null, updateWrapper);
        if (updateResult <= 0) {
            log.error("更新对口型记录状态失败: taskId={}", taskId);
            throw new BizException("更新对口型记录状态失败");
        }

        log.info("对口型记录状态更新成功: taskId={}", taskId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLipSyncRecordToFailed(String taskId, String errorMessage) {
        log.info("更新对口型记录为失败状态: taskId={}, errorMessage={}", taskId, errorMessage);

        try {
            // 1. 查询对口型记录获取分镜ID
            LambdaQueryWrapper<AiShotLipSyncPo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AiShotLipSyncPo::getTaskId, taskId)
                    .eq(AiShotLipSyncPo::getDelFlag, 0);

            AiShotLipSyncPo lipSyncRecord = shotLipSyncMapper.selectOne(queryWrapper);
            if (lipSyncRecord == null) {
                log.warn("未找到对口型记录: taskId={}", taskId);
                return;
            }

            // 2. 更新对口型记录状态
            LambdaUpdateWrapper<AiShotLipSyncPo> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(AiShotLipSyncPo::getTaskId, taskId)
                    .set(AiShotLipSyncPo::getStatus, "FAILED")
                    .set(AiShotLipSyncPo::getErrorMessage, errorMessage)
                    .set(AiShotLipSyncPo::getUpdateTime, new Date());

            int updateResult = shotLipSyncMapper.update(null, updateWrapper);
            if (updateResult <= 0) {
                log.error("更新对口型记录为失败状态失败: taskId={}", taskId);
                throw new BizException("更新对口型记录为失败状态失败");
            }

            log.info("对口型记录失败状态更新成功: taskId={}", taskId);

            // 3. 更新分镜状态为失败
            updateShotStatus(lipSyncRecord.getShotId(), ShotStatus.FAILED.getValue());

        } catch (Exception e) {
            log.error("更新对口型记录为失败状态异常: taskId={}, error={}", taskId, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleLipSyncCallback(String taskId, Object eventData) {
        log.info("处理对口型回调: taskId={}", taskId);

        try {
            // 转换事件数据
            ComfyUICallbackEventData callbackEventData = (ComfyUICallbackEventData) eventData;

            // 获取结果数据列表
            List<ComfyUICallbackEventData.ComfyUIResultData> resultDataList = callbackEventData.getResultDataList();
            if (resultDataList == null || resultDataList.isEmpty()) {
                log.warn("对口型回调数据为空: taskId={}", taskId);
                updateLipSyncRecordToFailed(taskId, "回调数据为空");
                return;
            }

            // 查找第一个MP4文件
            ComfyUICallbackEventData.ComfyUIResultData mp4Result = null;
            for (ComfyUICallbackEventData.ComfyUIResultData resultData : resultDataList) {
                if ("mp4".equalsIgnoreCase(resultData.getFileType())) {
                    mp4Result = resultData;
                    break;
                }
            }

            if (mp4Result == null) {
                log.warn("对口型回调中未找到MP4文件: taskId={}", taskId);
                updateLipSyncRecordToFailed(taskId, "回调结果中未找到MP4文件");
                return;
            }

            log.info("找到对口型MP4结果: taskId={}, fileUrl={}", taskId, mp4Result.getFileUrl());

            // 查询对口型记录获取分镜ID和用户ID
            AiShotLipSyncPo lipSyncRecord = shotLipSyncMapper.selectOne(
                    new LambdaQueryWrapper<AiShotLipSyncPo>()
                            .eq(AiShotLipSyncPo::getTaskId, taskId)
            );

            if (lipSyncRecord == null) {
                log.error("对口型记录不存在: taskId={}", taskId);
                return;
            }

            // 上传视频到OSS
            String ossVideoUrl = uploadVideoToOss(mp4Result.getFileUrl(), lipSyncRecord.getUserId());

            // 更新对口型记录状态
            updateLipSyncRecordStatus(taskId, "COMPLETED", ossVideoUrl, mp4Result.getTaskCostTime());

            // 更新分镜状态和类型
            updateShotStatusAndType(lipSyncRecord.getShotId());

            // 更新或创建画布视频记录
            updateOrCreateCanvasVideo(lipSyncRecord.getShotId(), ossVideoUrl);

            // 扣除用户积分（对口型成功扣除150积分）
            try {
                userPointsService.deductUserPoints(
                        lipSyncRecord.getUserId(),
                        400,
                        taskId,
                        "对口型成功",
                        TransactionTypeEnum.LIP_SYNC
                );
                log.info("对口型积分扣除成功: userId={}, taskId={}, points=150",
                        lipSyncRecord.getUserId(), taskId);
            } catch (Exception e) {
                log.error("对口型积分扣除失败: userId={}, taskId={}, error={}",
                        lipSyncRecord.getUserId(), taskId, e.getMessage(), e);
                // 积分扣除失败不影响主流程，只记录日志
            }

            // 对口型成功后，将当前分镜下所有音频的音量设置为0
            try {
                setAllAudioVolumesToZero(lipSyncRecord.getShotId());
                log.info("对口型成功后音频音量设置为0完成: shotId={}", lipSyncRecord.getShotId());
            } catch (Exception e) {
                log.error("对口型成功后设置音频音量为0失败: shotId={}, error={}",
                        lipSyncRecord.getShotId(), e.getMessage(), e);
                // 音量设置失败不影响主流程，只记录日志
            }

            log.info("对口型回调处理完成: taskId={}, shotId={}, ossVideoUrl={}",
                    taskId, lipSyncRecord.getShotId(), ossVideoUrl);

        } catch (Exception e) {
            log.error("处理对口型回调异常: taskId={}", taskId, e);
            updateLipSyncRecordToFailed(taskId, "处理回调异常: " + e.getMessage());
        }
    }

    /**
     * 上传视频文件到OSS
     */
    private String uploadVideoToOss(String videoUrl, String userId) {
        try {
            log.info("开始上传视频到OSS: videoUrl={}, userId={}", videoUrl, userId);

            // 构建OSS路径
            String ossPath = OSS_VIDEO_PATH.replace("{env}", env)
                    .replace("{userId}", userId)
                    .replace("{type}", "lip-sync");

            // 生成唯一文件名
            String fileName = "lipsync_" + IdUtil.fastSimpleUUID() + ".mp4";
            String fullOssPath = ossPath + fileName;

            // 调用OSS工具类上传文件
            String fullOssUrl = ossUtils.uploadFile(videoUrl, fullOssPath);


            log.info("视频上传到OSS成功: originalUrl={}, ossUrl={}", videoUrl, fullOssUrl);
            return fullOssUrl;

        } catch (Exception e) {
            log.error("上传视频到OSS失败: videoUrl={}, userId={}", videoUrl, userId, e);
            throw new BizException("上传视频到OSS失败: " + e.getMessage());
        }
    }

    /**
     * 更新分镜状态
     */
    private void updateShotStatus(Long shotId, Integer status) {
        try {
            log.info("更新分镜状态: shotId={}, status={}", shotId, status);

            // 获取分镜对象并更新
            AiCanvasShotPo shotPo = aiCanvasShotService.getById(shotId);
            if (shotPo == null) {
                log.error("分镜不存在: shotId={}", shotId);
                throw new BizException("分镜不存在");
            }

            shotPo.setShotStatus(status);
            shotPo.setUpdateTime(new Date());

            boolean updateResult = aiCanvasShotService.updateById(shotPo);
            if (!updateResult) {
                log.error("更新分镜状态失败: shotId={}, status={}", shotId, status);
                throw new BizException("更新分镜状态失败");
            }

            log.info("分镜状态更新成功: shotId={}, status={}", shotId, status);

        } catch (Exception e) {
            log.error("更新分镜状态异常: shotId={}, status={}, error={}", shotId, status, e.getMessage(), e);
            throw new BizException("更新分镜状态失败: " + e.getMessage());
        }
    }

    /**
     * 更新分镜状态和类型
     */
    private void updateShotStatusAndType(Long shotId) {
        try {
            log.info("更新分镜状态和类型: shotId={}", shotId);

            // 获取分镜对象并更新
            AiCanvasShotPo shotPo = aiCanvasShotService.getById(shotId);
            if (shotPo == null) {
                log.error("分镜不存在: shotId={}", shotId);
                throw new BizException("分镜不存在");
            }

            shotPo.setType("video");
            shotPo.setShotStatus(2); // 已完成
            shotPo.setUpdateTime(new Date());

            boolean updateResult = aiCanvasShotService.updateById(shotPo);
            if (!updateResult) {
                log.error("更新分镜状态和类型失败: shotId={}", shotId);
                throw new BizException("更新分镜状态和类型失败");
            }

            log.info("分镜状态和类型更新成功: shotId={}", shotId);

        } catch (Exception e) {
            log.error("更新分镜状态和类型异常: shotId={}", shotId, e);
            throw new BizException("更新分镜状态和类型异常: " + e.getMessage());
        }
    }

    /**
     * 更新或创建画布视频记录
     */
    private void updateOrCreateCanvasVideo(Long shotId, String videoUrl) {
        try {
            log.info("更新或创建画布视频记录: shotId={}, videoUrl={}", shotId, videoUrl);

            // 查询分镜信息获取画布ID和分镜编码
            AiCanvasShotPo shotPo = aiCanvasShotService.getById(shotId);
            if (shotPo == null) {
                log.error("分镜不存在: shotId={}", shotId);
                throw new BizException("分镜不存在");
            }

            // 查询是否已存在视频记录
            AiCanvasVideoPo existingVideo = aiCanvasVideoMapper.selectOne(
                    new LambdaQueryWrapper<AiCanvasVideoPo>()
                            .eq(AiCanvasVideoPo::getCanvasId, shotPo.getCanvasId())
                            .eq(AiCanvasVideoPo::getShotCode, shotPo.getCode())
            );

            if (existingVideo != null) {
                // 更新现有记录
                LambdaUpdateWrapper<AiCanvasVideoPo> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(AiCanvasVideoPo::getId, existingVideo.getId())
                        .set(AiCanvasVideoPo::getVideoUrl, videoUrl)
                        .set(AiCanvasVideoPo::getVideoStatus, "COMPLETED")
                        .set(AiCanvasVideoPo::getUpdateTime, new Date());

                int updateResult = aiCanvasVideoMapper.update(null, updateWrapper);
                if (updateResult <= 0) {
                    log.error("更新画布视频记录失败: shotId={}, videoId={}", shotId, existingVideo.getId());
                    throw new BizException("更新画布视频记录失败");
                }

                log.info("画布视频记录更新成功: shotId={}, videoId={}", shotId, existingVideo.getId());

            } else {
                // 创建新记录
                AiCanvasVideoPo newVideo = new AiCanvasVideoPo();
                newVideo.setCanvasId(shotPo.getCanvasId());
                newVideo.setShotCode(shotPo.getCode());
                newVideo.setVideoUrl(videoUrl);
                newVideo.setVideoStatus("COMPLETED");
                newVideo.setVolume(new java.math.BigDecimal("1.0")); // 默认音量
                newVideo.setCreateTime(new Date());
                newVideo.setUpdateTime(new Date());
                newVideo.setDelFlag(0);

                int insertResult = aiCanvasVideoMapper.insert(newVideo);
                if (insertResult <= 0) {
                    log.error("创建画布视频记录失败: shotId={}", shotId);
                    throw new BizException("创建画布视频记录失败");
                }

                log.info("画布视频记录创建成功: shotId={}, videoId={}", shotId, newVideo.getId());
            }

            // 创建画布素材记录
            AiCanvasMaterialPo materialPo = new AiCanvasMaterialPo();
            materialPo.setCanvasId(shotPo.getCanvasId());
            materialPo.setMaterialType(2); // 1-图片
            materialPo.setMaterialSource(1); // 1-生成
            materialPo.setMaterialUrl(videoUrl);
            materialPo.setCreateTime(new Date());
            materialPo.setUpdateTime(new Date());
            materialPo.setDelFlag(0);
            aiCanvasMaterialMapper.insert(materialPo);


        } catch (Exception e) {
            log.error("更新或创建画布视频记录异常: shotId={}, videoUrl={}", shotId, videoUrl, e);
            throw new BizException("更新或创建画布视频记录异常: " + e.getMessage());
        }
    }

    /**
     * 将指定分镜下的所有音频音量设置为0
     *
     * @param shotId 分镜ID
     */
    private void setAllAudioVolumesToZero(Long shotId) {
        log.info("开始将分镜下所有音频音量设置为0: shotId={}", shotId);

        try {
            // 查询分镜信息获取画布ID和分镜编码
            AiCanvasShotPo shotPo = aiCanvasShotService.getById(shotId);
            if (shotPo == null) {
                log.error("分镜不存在: shotId={}", shotId);
                throw new BizException("分镜不存在");
            }

            // 更新该分镜下所有音频的音量为0
            LambdaUpdateWrapper<AiCanvasAudioPo> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(AiCanvasAudioPo::getCanvasId, shotPo.getCanvasId())
                    .eq(AiCanvasAudioPo::getShotCode, shotPo.getCode())
                    .eq(AiCanvasAudioPo::getDelFlag, 0)
                    .set(AiCanvasAudioPo::getVolume, new java.math.BigDecimal("0.00"))
                    .set(AiCanvasAudioPo::getUpdateTime, new Date());

            int updateCount = canvasAudioMapper.update(null, updateWrapper);

            log.info("分镜下音频音量设置为0完成: shotId={}, canvasId={}, shotCode={}, updateCount={}",
                    shotId, shotPo.getCanvasId(), shotPo.getCode(), updateCount);

        } catch (Exception e) {
            log.error("设置分镜下音频音量为0异常: shotId={}", shotId, e);
            throw new BizException("设置音频音量为0异常: " + e.getMessage());
        }
    }
}
