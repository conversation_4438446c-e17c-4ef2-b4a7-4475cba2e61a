package com.wlink.agent.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wlink.agent.dao.mapper.*;
import com.wlink.agent.dao.po.*;
import com.wlink.agent.dao.mapper.AiUsersMapper;
import com.alibaba.cola.exception.BizException;
import com.wlink.agent.common.dto.PageRes;
import com.wlink.agent.model.dto.CanvasRenderDataDto;
import com.wlink.agent.model.dto.PythonRenderResponse;
import com.wlink.agent.model.req.VideoRenderExportReq;
import com.wlink.agent.model.res.VideoRenderExportRes;
import com.wlink.agent.model.res.CanvasBackgroundMusicRes;
import com.wlink.agent.service.VideoRenderExportService;
import com.wlink.agent.service.CanvasBackgroundMusicService;
import com.wlink.agent.utils.MediaUrlPrefixUtil;
import com.wlink.agent.utils.OssUtils;
import com.wlink.agent.utils.ShareCodeGenerator;
import com.wlink.agent.utils.UserContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 视频渲染导出服务实现
 */
@Slf4j
@Service
@RefreshScope
@RequiredArgsConstructor
public class VideoRenderExportServiceImpl extends ServiceImpl<AiVideoRenderExportMapper, AiVideoRenderExportPo> 
        implements VideoRenderExportService {

    private final AiCanvasMapper canvasMapper;
    private final AiCanvasShotMapper canvasShotMapper;
    private final AiCanvasImageMapper canvasImageMapper;
    private final AiCanvasVideoMapper canvasVideoMapper;
    private final AiCanvasAudioMapper canvasAudioMapper;
    private final CanvasBackgroundMusicService canvasBackgroundMusicService;
    private final OkHttpClient okHttpClient;
    private final OssUtils ossUtils;
    private final ShareCodeGenerator shareCodeGenerator;
    private final AiUsersMapper aiUsersMapper;
    private final AiCreationSessionMapper aiCreationSessionMapper;

    @Value("${video.render.url:http://47.117.139.188:9990/api/tasks}")
    private String pythonRenderUrl;


    //视频分享url
    @Value("${video.share.url:https://dev.neodomain.cn/videoShare?shareCode={shareCode}}")
    private String videoShareUrl;


    private static final String OSS_PATH = "dify/{env}/{userId}/{type}/";
    @Value("${spring.profiles.active}")
    String env;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long submitRenderExport(VideoRenderExportReq req) {
        String userId = UserContext.getUser().getUserId();
        log.info("用户 {} 提交视频渲染导出任务: canvasId={}, resolution={}",
                userId, req.getCanvasId(), req.getResolution());

        // 1. 验证画布是否存在且属于当前用户
        AiCanvasPo canvasPo = canvasMapper.selectById(req.getCanvasId());
        if (canvasPo == null || canvasPo.getDelFlag() == 1) {
            throw new BizException("画布不存在");
        }
        if (!userId.equals(canvasPo.getUserId())) {
            throw new BizException("无权限操作此画布");
        }

        // 2. 生成画布渲染数据JSON
        CanvasRenderDataDto renderData = generateCanvasRenderData(req.getCanvasId(), req.getResolution(), canvasPo.getRatio(), req.getShowSubtitle(),req.getFps());
        String canvasDataJson = JSON.toJSONString(renderData);

        // 3. 先调用Python渲染接口
        String renderTaskId = callPythonRenderApi(canvasDataJson);
        if (!StringUtils.hasText(renderTaskId)) {
            throw new BizException("调用Python渲染接口失败");
        }

        // 4. 调用成功后创建渲染导出记录
        AiVideoRenderExportPo exportPo = new AiVideoRenderExportPo();
        exportPo.setUserId(userId);
        exportPo.setCanvasId(req.getCanvasId());
        exportPo.setResolution(req.getResolution());
        exportPo.setRatio(canvasPo.getRatio());
        exportPo.setShowSubtitle(req.getShowSubtitle() != null ? req.getShowSubtitle() : 0);
        exportPo.setRenderTaskId(renderTaskId); // 设置从Python接口返回的任务ID
        exportPo.setStatus(0); // 0-排队中
        exportPo.setCanvasDataJson(canvasDataJson);
        exportPo.setShareStatus(0); // 初始化为未分享
        exportPo.setCreateTime(new Date());
        exportPo.setUpdateTime(new Date());
        exportPo.setDelFlag(0);

        this.save(exportPo);

        log.info("视频渲染导出任务提交成功: taskId={}", exportPo.getId());
        return exportPo.getId();
    }

    @Override
    public List<VideoRenderExportRes> getUserRenderExports(String userId, Long canvasId) {
        LambdaQueryWrapper<AiVideoRenderExportPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiVideoRenderExportPo::getUserId, userId)
                .eq(AiVideoRenderExportPo::getDelFlag, 0);

        // 如果传了画布ID，则添加画布ID条件
        if (canvasId != null) {
            queryWrapper.eq(AiVideoRenderExportPo::getCanvasId, canvasId);
        }

        queryWrapper.orderByDesc(AiVideoRenderExportPo::getCreateTime)
                .last("limit 6"); // 只返回最近的6条记录

        List<AiVideoRenderExportPo> exportList = this.list(queryWrapper);
        return exportList.stream()
                .map(this::convertToRes)
                .collect(Collectors.toList());
    }

    @Override
    public VideoRenderExportRes getRenderExportDetail(Long taskId) {
        AiVideoRenderExportPo exportPo = this.getById(taskId);
        if (exportPo == null || exportPo.getDelFlag() == 1) {
            throw new BizException("渲染任务不存在");
        }

        // 验证权限
        String currentUserId = UserContext.getUser().getUserId();
        if (!currentUserId.equals(exportPo.getUserId())) {
            throw new BizException("无权限查看此渲染任务");
        }

        return convertToRes(exportPo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setRenderTaskId(Long taskId, String renderTaskId) {
        AiVideoRenderExportPo exportPo = this.getById(taskId);
        if (exportPo == null) {
            log.error("设置渲染任务ID时未找到记录: taskId={}", taskId);
            return;
        }

        exportPo.setRenderTaskId(renderTaskId);
        exportPo.setUpdateTime(new Date());
        this.updateById(exportPo);

        log.info("设置渲染任务ID成功: taskId={}, renderTaskId={}", taskId, renderTaskId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void startProcessing(Long taskId) {
        AiVideoRenderExportPo exportPo = this.getById(taskId);
        if (exportPo == null) {
            log.error("开始处理时未找到渲染任务: taskId={}", taskId);
            return;
        }

        exportPo.setStatus(1); // 1-渲染中
        exportPo.setStartTime(new Date());
        exportPo.setUpdateTime(new Date());
        this.updateById(exportPo);

        log.info("开始处理视频渲染任务: taskId={}", taskId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void completeTask(Long taskId, String videoUrl) {
        AiVideoRenderExportPo exportPo = this.getById(taskId);
        if (exportPo == null) {
            log.error("完成任务时未找到渲染记录: taskId={}", taskId);
            return;
        }

        exportPo.setStatus(2); // 2-已完成
        exportPo.setVideoUrl(videoUrl);
        exportPo.setCompleteTime(new Date());
        exportPo.setUpdateTime(new Date());
        this.updateById(exportPo);

        log.info("视频渲染任务完成: taskId={}, videoUrl={}", taskId, videoUrl);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void failTask(Long taskId, String errorMessage) {
        AiVideoRenderExportPo exportPo = this.getById(taskId);
        if (exportPo == null) {
            log.error("失败任务时未找到渲染记录: taskId={}", taskId);
            return;
        }

        exportPo.setStatus(3); // 3-失败
        exportPo.setErrorMessage(errorMessage);
        exportPo.setCompleteTime(new Date());
        exportPo.setUpdateTime(new Date());
        this.updateById(exportPo);

        log.error("视频渲染任务失败: taskId={}, error={}", taskId, errorMessage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean handleRenderCallback(String renderTaskId, Integer status, String errorMessage, String videoUrl,Long videoDuration) {
        if (!StringUtils.hasText(renderTaskId)) {
            log.error("渲染任务ID不能为空");
            return false;
        }

        if (status == null) {
            log.error("渲染状态不能为空");
            return false;
        }

        // 根据renderTaskId查询渲染记录
        LambdaQueryWrapper<AiVideoRenderExportPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiVideoRenderExportPo::getRenderTaskId, renderTaskId)
                .eq(AiVideoRenderExportPo::getDelFlag, 0);

        AiVideoRenderExportPo exportPo = this.getOne(queryWrapper);
        if (exportPo == null) {
            log.error("未找到对应的渲染记录: renderTaskId={}", renderTaskId);
            return false;
        }

        // 更新渲染记录状态
        exportPo.setStatus(status);
        exportPo.setUpdateTime(new Date());

        // 根据状态更新相应字段
        switch (status) {
            case 1: // 渲染中
                if (exportPo.getStartTime() == null) {
                    exportPo.setStartTime(new Date());
                }
                log.info("渲染任务开始处理: renderTaskId={}, taskId={}", renderTaskId, exportPo.getId());
                break;

            case 2: // 已完成
                exportPo.setCompleteTime(new Date());
                if (StringUtils.hasText(videoUrl)) {
                    String video = ossUtils.uploadFile(videoUrl, OSS_PATH.replace("{env}", env)
                            .replace("{userId}", exportPo.getUserId())
                            .replace("{type}", "video") + IdUtil.fastSimpleUUID() + ".mp4");
                    exportPo.setVideoUrl(video);
                    exportPo.setVideoDuration(videoDuration);

                }
                exportPo.setErrorMessage(null); // 清除之前的错误信息
                log.info("渲染任务完成: renderTaskId={}, taskId={}, videoUrl={}",
                        renderTaskId, exportPo.getId(), videoUrl);
                break;

            case 3: // 失败
                exportPo.setCompleteTime(new Date());
                if (StringUtils.hasText(errorMessage)) {
                    exportPo.setErrorMessage(errorMessage);
                }
                log.error("渲染任务失败: renderTaskId={}, taskId={}, error={}",
                        renderTaskId, exportPo.getId(), errorMessage);
                break;

            default:
                log.warn("未知的渲染状态: renderTaskId={}, status={}", renderTaskId, status);
                return false;
        }

        // 更新数据库记录
        boolean updateResult = this.updateById(exportPo);
        if (updateResult) {
            log.info("渲染回调处理成功: renderTaskId={}, taskId={}, status={}",
                    renderTaskId, exportPo.getId(), status);
        } else {
            log.error("渲染回调处理失败，数据库更新失败: renderTaskId={}, taskId={}",
                    renderTaskId, exportPo.getId());
        }

        return updateResult;
    }

    @Override
    @Async
    public void handleRenderCallbackAsync(String renderTaskId, Integer status, String errorMessage, String videoUrl, Long videoDuration) {
        log.info("开始异步处理视频渲染回调: renderTaskId={}, status={}", renderTaskId, status);

        try {
            boolean success = handleRenderCallback(renderTaskId, status, errorMessage, videoUrl, videoDuration);
            if (success) {
                log.info("异步处理视频渲染回调成功: renderTaskId={}", renderTaskId);
            } else {
                log.error("异步处理视频渲染回调失败: renderTaskId={}", renderTaskId);
            }
        } catch (Exception e) {
            log.error("异步处理视频渲染回调异常: renderTaskId={}, error={}", renderTaskId, e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRenderExport(Long taskId) {
        if (taskId == null) {
            throw new BizException("任务ID不能为空");
        }

        // 查询渲染记录是否存在
        AiVideoRenderExportPo exportPo = this.getById(taskId);
        if (exportPo == null || exportPo.getDelFlag() == 1) {
            log.error("渲染记录不存在或已被删除: taskId={}", taskId);
            throw new BizException("渲染记录不存在或已被删除");
        }

        // 验证用户权限
        String currentUserId = UserContext.getUser().getUserId();
        if (!currentUserId.equals(exportPo.getUserId())) {
            log.error("用户 {} 无权限删除渲染记录 {}", currentUserId, taskId);
            throw new BizException("无权限删除此渲染记录");
        }

        // 检查渲染状态，只能删除成功(2)和失败(3)的记录
        Integer status = exportPo.getStatus();
        if (status == null || (status != 2 && status != 3)) {
            log.error("只能删除已完成或失败的渲染记录: taskId={}, status={}", taskId, status);
            throw new BizException("只能删除已完成或失败的渲染记录");
        }
        this.removeById(taskId);
    }

    /**
     * 生成画布渲染数据
     */
    private CanvasRenderDataDto generateCanvasRenderData(Long canvasId, String resolution, String ratio, Integer showSubtitle,Integer fps) {
        // 查询画布信息
        AiCanvasPo canvasPo = canvasMapper.selectById(canvasId);
        
        CanvasRenderDataDto renderData = new CanvasRenderDataDto();
        renderData.setCanvasId(canvasId);
        renderData.setCanvasName(canvasPo.getCanvasName());
        renderData.setResolution(resolution);
        renderData.setRatio(ratio);
        renderData.setShowSubtitle(showSubtitle != null ? showSubtitle : 0);
        renderData.setFps(fps != null ? fps : 24);

        // 查询分镜列表
        LambdaQueryWrapper<AiCanvasShotPo> shotQueryWrapper = new LambdaQueryWrapper<>();
        shotQueryWrapper.eq(AiCanvasShotPo::getCanvasId, canvasId)
                .eq(AiCanvasShotPo::getDelFlag, 0)
                .orderByAsc(AiCanvasShotPo::getSortOrder);

        List<AiCanvasShotPo> shotList = canvasShotMapper.selectList(shotQueryWrapper);
        
        List<CanvasRenderDataDto.ShotRenderDataDto> shotDataList = new ArrayList<>();
        
        for (AiCanvasShotPo shotPo : shotList) {
            CanvasRenderDataDto.ShotRenderDataDto shotData = new CanvasRenderDataDto.ShotRenderDataDto();
            shotData.setCode(shotPo.getCode());
            shotData.setType(shotPo.getType());
            shotData.setSortOrder(shotPo.getSortOrder());
            shotData.setComposition(shotPo.getComposition());
            shotData.setDisplayType(shotPo.getDisplayType());
            shotData.setMovement(shotPo.getMovement());

            // 根据分镜类型查询对应的数据
            if ("image".equals(shotPo.getType())) {
                shotData.setImageData(getImageData(canvasId, shotPo.getCode()));
            } else if ("video".equals(shotPo.getType())) {
                shotData.setVideoData(getVideoData(canvasId, shotPo.getCode()));
            }

            // 查询音频数据
            shotData.setAudios(getAudioData(canvasId, shotPo.getCode()));

            shotDataList.add(shotData);
        }

        renderData.setShots(shotDataList);

        // 查询背景音乐数据
        CanvasRenderDataDto.BackgroundMusicRenderDataDto backgroundMusicData = getBackgroundMusicData(canvasId);
        renderData.setBackgroundMusic(backgroundMusicData);

        return renderData;
    }

    /**
     * 获取图片数据
     */
    private CanvasRenderDataDto.ImageRenderDataDto getImageData(Long canvasId, String shotCode) {
        LambdaQueryWrapper<AiCanvasImagePo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiCanvasImagePo::getCanvasId, canvasId)
                .eq(AiCanvasImagePo::getShotCode, shotCode)
                .eq(AiCanvasImagePo::getDelFlag, 0);

        AiCanvasImagePo imagePo = canvasImageMapper.selectOne(queryWrapper);
        if (imagePo == null) {
            return null;
        }

        CanvasRenderDataDto.ImageRenderDataDto imageData = new CanvasRenderDataDto.ImageRenderDataDto();
        imageData.setImageUrl(MediaUrlPrefixUtil.getMediaUrl(imagePo.getImageUrl()));
        imageData.setImageAspectRatio(imagePo.getImageAspectRatio());
        imageData.setImageStatus(imagePo.getImageStatus());
        imageData.setReferenceImage(imagePo.getReferenceImage() != null ?
                MediaUrlPrefixUtil.getMediaUrl(imagePo.getReferenceImage()) : null);

        return imageData;
    }

    /**
     * 获取视频数据
     */
    private CanvasRenderDataDto.VideoRenderDataDto getVideoData(Long canvasId, String shotCode) {
        LambdaQueryWrapper<AiCanvasVideoPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiCanvasVideoPo::getCanvasId, canvasId)
                .eq(AiCanvasVideoPo::getShotCode, shotCode)
                .eq(AiCanvasVideoPo::getDelFlag, 0);

        AiCanvasVideoPo videoPo = canvasVideoMapper.selectOne(queryWrapper);
        if (videoPo == null) {
            return null;
        }

        CanvasRenderDataDto.VideoRenderDataDto videoData = new CanvasRenderDataDto.VideoRenderDataDto();
        videoData.setVideoUrl(MediaUrlPrefixUtil.getMediaUrl(videoPo.getVideoUrl()));
        videoData.setVideoDuration(videoPo.getVideoDuration());
        videoData.setVideoAspectRatio(videoPo.getVideoAspectRatio());
        videoData.setVideoStatus(videoPo.getVideoStatus());
        videoData.setStartFrameImage(videoPo.getStartFrameImage() != null ?
                MediaUrlPrefixUtil.getMediaUrl(videoPo.getStartFrameImage()) : null);
        videoData.setEndFrameImage(videoPo.getEndFrameImage() != null ?
                MediaUrlPrefixUtil.getMediaUrl(videoPo.getEndFrameImage()) : null);
        videoData.setVolume(videoPo.getVolume() != null ? videoPo.getVolume() : new java.math.BigDecimal("1.00")); // 设置视频音量

        return videoData;
    }

    /**
     * 获取音频数据 (按sortOrder排序)
     */
    private List<CanvasRenderDataDto.AudioRenderDataDto> getAudioData(Long canvasId, String shotCode) {
        LambdaQueryWrapper<AiCanvasAudioPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiCanvasAudioPo::getCanvasId, canvasId)
                .eq(AiCanvasAudioPo::getShotCode, shotCode)
                .eq(AiCanvasAudioPo::getDelFlag, 0)
                .orderByAsc(AiCanvasAudioPo::getSortOrder); // 重要：按sortOrder排序

        List<AiCanvasAudioPo> audioList = canvasAudioMapper.selectList(queryWrapper);

        return audioList.stream().map(audioPo -> {
            CanvasRenderDataDto.AudioRenderDataDto audioData = new CanvasRenderDataDto.AudioRenderDataDto();
            audioData.setAudioUrl(MediaUrlPrefixUtil.getMediaUrl(audioPo.getAudioUrl()));
            audioData.setAudioType(audioPo.getAudioType());
            audioData.setText(Objects.equals(audioPo.getAudioType(),2) ? "" : audioPo.getText());
            audioData.setVoiceId(audioPo.getVoiceId());
            audioData.setAudioDuration(audioPo.getAudioDuration());
            audioData.setSortOrder(audioPo.getSortOrder());
            audioData.setVolume(audioPo.getVolume() != null ? audioPo.getVolume() : new java.math.BigDecimal("1.00")); // 设置音频音量
            return audioData;
        }).collect(Collectors.toList());
    }

    /**
     * 获取背景音乐数据
     */
    private CanvasRenderDataDto.BackgroundMusicRenderDataDto getBackgroundMusicData(Long canvasId) {
        CanvasBackgroundMusicRes backgroundMusic = canvasBackgroundMusicService.getCanvasBackgroundMusic(canvasId);
        if (backgroundMusic == null) {
            return null;
        }

        CanvasRenderDataDto.BackgroundMusicRenderDataDto backgroundMusicData = new CanvasRenderDataDto.BackgroundMusicRenderDataDto();
        backgroundMusicData.setAudioUrl(backgroundMusic.getAudioUrl());
        backgroundMusicData.setName(backgroundMusic.getName());
        backgroundMusicData.setAudioDuration(backgroundMusic.getAudioDuration());
        backgroundMusicData.setStartTime(backgroundMusic.getStartTime());
        backgroundMusicData.setEndTime(backgroundMusic.getEndTime());
        backgroundMusicData.setStartTrackTime(backgroundMusic.getStartTrackTime());
        backgroundMusicData.setVolume(backgroundMusic.getVolume());
        backgroundMusicData.setFadeInTime(backgroundMusic.getFadeInTime());
        backgroundMusicData.setFadeOutTime(backgroundMusic.getFadeOutTime());
        backgroundMusicData.setIsLoop(backgroundMusic.getIsLoop());
        backgroundMusicData.setAudioFormat(backgroundMusic.getAudioFormat());

        return backgroundMusicData;
    }

    /**
     * 调用Python渲染接口
     *
     * @param canvasDataJson 画布数据JSON
     * @return 渲染任务ID，失败返回null
     */
    private String callPythonRenderApi(String canvasDataJson) {
        log.info("调用Python渲染接口");
        log.debug("发送的画布数据JSON: {}", canvasDataJson);

        try {
            // 调用Python渲染接口
            String pythonApiUrl = pythonRenderUrl;

            // 创建请求体
            RequestBody requestBody = RequestBody.create(
                    canvasDataJson,
                    MediaType.get("application/json; charset=utf-8")
            );

            // 构建请求
            Request request = new Request.Builder()
                    .url(pythonApiUrl)
                    .post(requestBody)
                    .addHeader("Content-Type", "application/json")
                    .build();

            // 发送请求
            try (Response response = okHttpClient.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    log.debug("Python渲染接口响应: {}", responseBody);

                    // 解析响应JSON
                    PythonRenderResponse pythonResponse = JSON.parseObject(responseBody, PythonRenderResponse.class);

                    if (pythonResponse != null && Boolean.TRUE.equals(pythonResponse.getSuccess()) && pythonResponse.getCode() == 200) {
                        // 调用成功，返回renderTaskId
                        String renderTaskId = pythonResponse.getTraceId();
                        if (StringUtils.hasText(renderTaskId)) {
                            log.info("Python渲染接口调用成功: renderTaskId={}, message={}",
                                    renderTaskId, pythonResponse.getMessage());
                            return renderTaskId;
                        } else {
                            log.error("Python渲染接口返回的traceId为空: response={}", pythonResponse);
                            return null;
                        }
                    } else {
                        // 调用失败
                        String errorMsg = pythonResponse != null ? pythonResponse.getMessage() : "未知错误";
                        log.error("Python渲染接口调用失败: response={}", pythonResponse);
                        return null;
                    }
                } else {
                    log.error("Python渲染接口HTTP请求失败: code={}, message={}",
                            response.code(), response.message());
                    return null;
                }
            }

        } catch (Exception e) {
            log.error("调用Python渲染接口异常: error={}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Mock Python渲染接口调用 (已废弃，保留用于测试)
     */
    @Deprecated
    private void mockPythonRenderCall(Long taskId, String canvasDataJson) {
        log.info("调用Python渲染接口: taskId={}", taskId);
        log.debug("发送的画布数据JSON: {}", canvasDataJson);

        try {
            // 调用Python渲染接口
            String pythonApiUrl = "http://localhost:8080/api/tasks";

            // 创建请求体
            RequestBody requestBody = RequestBody.create(
                    canvasDataJson,
                    MediaType.get("application/json; charset=utf-8")
            );

            // 构建请求
            Request request = new Request.Builder()
                    .url(pythonApiUrl)
                    .post(requestBody)
                    .addHeader("Content-Type", "application/json")
                    .build();

            // 发送请求
            try (Response response = okHttpClient.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    log.debug("Python渲染接口响应: {}", responseBody);

                    // 解析响应JSON
                    PythonRenderResponse pythonResponse = JSON.parseObject(responseBody, PythonRenderResponse.class);

                    if (pythonResponse != null && Boolean.TRUE.equals(pythonResponse.getSuccess()) && pythonResponse.getCode() == 200) {
                        // 调用成功，更新renderTaskId
                        String renderTaskId = pythonResponse.getTraceId();
                        if (StringUtils.hasText(renderTaskId)) {
                            setRenderTaskId(taskId, renderTaskId);
                            log.info("Python渲染接口调用成功: taskId={}, renderTaskId={}, message={}",
                                    taskId, renderTaskId, pythonResponse.getMessage());
                        } else {
                            log.error("Python渲染接口返回的traceId为空: taskId={}, response={}", taskId, pythonResponse);
                            failTask(taskId, "Python渲染接口返回的traceId为空");
                        }
                    } else {
                        // 调用失败
                        String errorMsg = pythonResponse != null ? pythonResponse.getMessage() : "未知错误";
                        log.error("Python渲染接口调用失败: taskId={}, response={}", taskId, pythonResponse);
                        failTask(taskId, "Python渲染接口调用失败: " + errorMsg);
                    }
                } else {
                    log.error("Python渲染接口HTTP请求失败: taskId={}, code={}, message={}",
                            taskId, response.code(), response.message());
                    failTask(taskId, "Python渲染接口HTTP请求失败: " + response.code() + " " + response.message());
                }
            }

        } catch (Exception e) {
            log.error("调用Python渲染接口异常: taskId={}, error={}", taskId, e.getMessage(), e);
            failTask(taskId, "调用Python渲染接口异常: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String shareVideo(Long taskId) {
        if (taskId == null) {
            throw new BizException("任务ID不能为空");
        }

        // 查询视频导出记录
        AiVideoRenderExportPo exportPo = this.getById(taskId);
        if (exportPo == null || exportPo.getDelFlag() == 1) {
            throw new BizException("视频导出记录不存在");
        }

        // 验证用户权限
        String currentUserId = UserContext.getUser().getUserId();
        if (!currentUserId.equals(exportPo.getUserId())) {
            throw new BizException("无权限操作此视频");
        }

        // 验证视频状态，只有已完成的视频才能分享
        if (exportPo.getStatus() != 2) {
            throw new BizException("只有渲染完成的视频才能分享");
        }

        // 如果已经分享过，直接返回分享码
        if (exportPo.getShareStatus() != null && exportPo.getShareStatus() == 1
                && StringUtils.hasText(exportPo.getShareCode())) {
            log.info("视频已分享，返回现有分享码: taskId={}, shareCode={}", taskId, exportPo.getShareCode());
            return exportPo.getShareCode();
        }

        // 生成分享码
        String shareCode = shareCodeGenerator.generateUniqueShareCode();

        // 更新分享状态
        exportPo.setShareStatus(1);
        exportPo.setShareCode(shareCode);
        exportPo.setShareTime(new Date());
        exportPo.setUpdateTime(new Date());
        this.updateById(exportPo);
        String shareUrl = videoShareUrl.replace("{shareCode}", shareCode);
        log.info("视频分享成功: taskId={}, shareUrl={}", taskId, shareUrl);
        return shareUrl;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unshareVideo(Long taskId) {
        if (taskId == null) {
            throw new BizException("任务ID不能为空");
        }

        // 查询视频导出记录
        AiVideoRenderExportPo exportPo = this.getById(taskId);
        if (exportPo == null || exportPo.getDelFlag() == 1) {
            throw new BizException("视频导出记录不存在");
        }

        // 验证用户权限
        String currentUserId = UserContext.getUser().getUserId();
        if (!currentUserId.equals(exportPo.getUserId())) {
            throw new BizException("无权限操作此视频");
        }

        // 取消分享
        exportPo.setShareStatus(0);
        exportPo.setShareCode("");
        exportPo.setShareTime(null);
        exportPo.setUpdateTime(new Date());
        this.updateById(exportPo);

        log.info("取消视频分享成功: taskId={}", taskId);
    }

    @Override
    public PageRes<VideoRenderExportRes> getSharedVideos(int pageNum, int pageSize) {
        // 创建分页对象
        IPage<AiVideoRenderExportPo> pageRequest = new Page<>(pageNum, pageSize);

        // 构建查询条件：只查询已分享的视频
        LambdaQueryWrapper<AiVideoRenderExportPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiVideoRenderExportPo::getShareStatus, 1)
                .eq(AiVideoRenderExportPo::getDelFlag, 0)
                .eq(AiVideoRenderExportPo::getStatus, 2) // 只查询已完成的视频
                .isNotNull(AiVideoRenderExportPo::getShareCode)
                .orderByDesc(AiVideoRenderExportPo::getShareTime);

        // 执行分页查询
        IPage<AiVideoRenderExportPo> pageResult = this.page(pageRequest, queryWrapper);

        // 转换结果
        List<VideoRenderExportRes> resList = pageResult.getRecords().stream()
                .map(this::convertToRes)
                .collect(Collectors.toList());

        // 构建分页响应
        return PageRes.of(
                (int) pageResult.getCurrent(),
                (int) pageResult.getSize(),
                pageResult.getTotal(),
                resList
        );
    }

    @Override
    public VideoRenderExportRes getVideoByShareCode(String shareCode) {
        if (!StringUtils.hasText(shareCode)) {
            throw new BizException("分享码不能为空");
        }

        // 根据分享码查询视频导出记录
        LambdaQueryWrapper<AiVideoRenderExportPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiVideoRenderExportPo::getShareCode, shareCode)
                .eq(AiVideoRenderExportPo::getShareStatus, 1)
                .eq(AiVideoRenderExportPo::getDelFlag, 0)
                .eq(AiVideoRenderExportPo::getStatus, 2); // 只查询已完成的视频

        AiVideoRenderExportPo exportPo = this.getOne(queryWrapper);
        if (exportPo == null) {
            throw new BizException("分享码无效或视频不存在");
        }

        log.info("根据分享码查询视频成功: shareCode={}, taskId={}", shareCode, exportPo.getId());
        return convertToRes(exportPo);
    }

    /**
     * 转换为响应对象
     */
    private VideoRenderExportRes convertToRes(AiVideoRenderExportPo exportPo) {
        VideoRenderExportRes res = new VideoRenderExportRes();
        BeanUtils.copyProperties(exportPo, res);
        res.setVideoUrl(MediaUrlPrefixUtil.getMediaUrl(res.getVideoUrl()));
        res.setFirstFrameUrl(MediaUrlPrefixUtil.getMediaUrl(res.getFirstFrameUrl()));

        // 设置分享URL
        if (StringUtils.hasText(res.getShareCode())){
            res.setShareUrl(videoShareUrl.replace("{shareCode}", res.getShareCode()));
        }

        // 设置分享状态，默认为未分享
        if (res.getShareStatus() == null) {
            res.setShareStatus(0);
        }

        // 根据画布ID查询画布名称和画布图片
        if (exportPo.getCanvasId() != null) {
            AiCanvasPo canvasPo = canvasMapper.selectById(exportPo.getCanvasId());
            if (canvasPo != null) {
                res.setCanvasName(canvasPo.getCanvasName());
                res.setCanvasCoverImage(MediaUrlPrefixUtil.getMediaUrl(canvasPo.getCoverImage()));
            }

            String sessionId = canvasPo.getSessionId();
            if (StringUtils.hasText(sessionId)) {
                LambdaQueryWrapper<AiCreationSessionPo> canvasQuery = new LambdaQueryWrapper<>();
                canvasQuery.eq(AiCreationSessionPo::getSessionId, sessionId)
                        .last("LIMIT 1");
                AiCreationSessionPo aiCreationSessionPo = aiCreationSessionMapper.selectOne(canvasQuery);
                if (aiCreationSessionPo != null) {
                    res.setPrompt(aiCreationSessionPo.getPrompt());
                }
            }

        }

        // 根据用户ID查询用户昵称
        if (StringUtils.hasText(exportPo.getUserId())) {
            LambdaQueryWrapper<AiUsersPo> userQuery = new LambdaQueryWrapper<>();
            userQuery.eq(AiUsersPo::getUserId, exportPo.getUserId())
                    .eq(AiUsersPo::getDelFlag, 0);
            AiUsersPo userPo = aiUsersMapper.selectOne(userQuery);
            if (userPo != null) {
                res.setUserNickname(userPo.getNickname());
                res.setUserAvatar(MediaUrlPrefixUtil.getMediaUrl(userPo.getAvatar()));
            }
        }

        return res;
    }
}
