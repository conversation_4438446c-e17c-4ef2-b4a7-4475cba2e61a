package com.wlink.agent.service.impl;

import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.wlink.agent.client.ComfyUIApiClient;
import com.wlink.agent.client.model.comfyui.ComfyUINodeInfo;
import com.wlink.agent.client.model.comfyui.ComfyUIRunRequest;
import com.wlink.agent.client.model.comfyui.ComfyUIRunResponse;
import com.wlink.agent.dao.mapper.AiAudioSynthesisTaskMapper;
import com.wlink.agent.dao.po.AiAudioSynthesisTaskPo;
import com.wlink.agent.model.dto.AudioSynthesisRequest;
import com.wlink.agent.model.dto.AudioSynthesisResult;
import com.wlink.agent.service.AudioSynthesisService;
import com.wlink.agent.service.ShotLipSyncService;
import com.wlink.agent.service.AiCanvasShotService;
import com.wlink.agent.utils.OssUtils;
import com.wlink.agent.enums.ShotStatus;
import com.wlink.agent.dao.po.AiCanvasShotPo;
import cn.hutool.core.util.IdUtil;
import com.wlink.agent.utils.MediaUrlPrefixUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 音频合成服务实现
 */
@Slf4j
@RefreshScope
@Service
@RequiredArgsConstructor
public class AudioSynthesisServiceImpl implements AudioSynthesisService {

    private final ComfyUIApiClient comfyUIApiClient;
    private final AiAudioSynthesisTaskMapper audioSynthesisTaskMapper;
    private final ShotLipSyncService shotLipSyncService;
    private final AiCanvasShotService aiCanvasShotService;
    private final OssUtils ossUtils;

    @Value("${spring.profiles.active}")
    private String env;

    /**
     * OSS路径模板
     */
    private static final String OSS_AUDIO_PATH = "dify/{env}/{userId}/{type}/";


    @Value("${comfyui.webhook-url:https://dev.neodomain.cn/agent/comfyui/callback}")
    private String webhookUrl;

    @Override
    public AudioSynthesisResult submitSynthesisTask(AudioSynthesisRequest request) {
        log.info("提交音频合成任务: shotId={}, audioCount={}", request.getShotId(), request.getAudioUrls().size());

        // 验证请求参数
        if (!request.isValid()) {
            throw new BizException("音频合成请求参数无效");
        }

        try {
            // 构建ComfyUI节点信息
            List<ComfyUINodeInfo> nodeInfoList = buildNodeInfoList(request.getAudioUrls());

            // 构建ComfyUI请求
            ComfyUIRunRequest comfyUIRequest = new ComfyUIRunRequest();
            comfyUIRequest.setWebappId(Long.valueOf(request.getWebappId()));
            comfyUIRequest.setApiKey(request.getApiKey());
            comfyUIRequest.setNodeInfoList(nodeInfoList);
            comfyUIRequest.setWebhookUrl(webhookUrl);

            // 调用ComfyUI API
            ComfyUIRunResponse response = comfyUIApiClient.runWorkflow(comfyUIRequest);

            if (!response.isSuccess() || response.getData() == null) {
                throw new BizException("ComfyUI音频合成任务提交失败: " + response.getMsg());
            }

            // 创建任务记录
            AiAudioSynthesisTaskPo taskPo = createTaskRecord(request, response);

            // 返回结果
            return AudioSynthesisResult.processing(
                    response.getData().getTaskId(),
                    request.getShotId(),
                    response.getData().getNetWssUrl(),
                    response.getData().getClientId()
            );

        } catch (Exception e) {
            log.error("提交音频合成任务失败: shotId={}", request.getShotId(), e);
            throw new BizException("提交音频合成任务失败: " + e.getMessage());
        }
    }

    @Override
    public AudioSynthesisResult getTaskStatus(String taskId) {
        log.debug("查询音频合成任务状态: taskId={}", taskId);

        AiAudioSynthesisTaskPo taskPo = audioSynthesisTaskMapper.selectOne(
                new LambdaQueryWrapper<AiAudioSynthesisTaskPo>()
                        .eq(AiAudioSynthesisTaskPo::getTaskId, taskId)
        );

        if (taskPo == null) {
            return null;
        }

        return convertToResult(taskPo);
    }

    @Override
    public AudioSynthesisResult getLatestTaskByShot(Long shotId) {
        log.debug("查询分镜最新音频合成任务: shotId={}", shotId);

        AiAudioSynthesisTaskPo taskPo = audioSynthesisTaskMapper.selectOne(
                new LambdaQueryWrapper<AiAudioSynthesisTaskPo>()
                        .eq(AiAudioSynthesisTaskPo::getShotId, shotId)
                        .orderByDesc(AiAudioSynthesisTaskPo::getCreateTime)
                        .last("LIMIT 1")
        );

        if (taskPo == null) {
            return null;
        }

        return convertToResult(taskPo);
    }

    @Override
    public boolean cancelTask(String taskId) {
        log.info("取消音频合成任务: taskId={}", taskId);

        int updateCount = audioSynthesisTaskMapper.update(null,
                new LambdaUpdateWrapper<AiAudioSynthesisTaskPo>()
                        .eq(AiAudioSynthesisTaskPo::getTaskId, taskId)
                        .in(AiAudioSynthesisTaskPo::getStatus, 
                            AiAudioSynthesisTaskPo.Status.PENDING, 
                            AiAudioSynthesisTaskPo.Status.PROCESSING)
                        .set(AiAudioSynthesisTaskPo::getStatus, AiAudioSynthesisTaskPo.Status.CANCELLED)
                        .set(AiAudioSynthesisTaskPo::getUpdateTime, new Date())
        );

        return updateCount > 0;
    }

    @Override
    public void handleCallback(String taskId, boolean success, String resultAudioUrl, String errorMessage, Long taskCostTime) {
        log.info("处理音频合成回调: taskId={}, success={}, resultUrl={}", taskId, success, resultAudioUrl);

        try {
            // 先查询任务信息，获取shotId
            AiAudioSynthesisTaskPo taskPo = audioSynthesisTaskMapper.selectOne(
                    new LambdaQueryWrapper<AiAudioSynthesisTaskPo>()
                            .eq(AiAudioSynthesisTaskPo::getTaskId, taskId)
            );

            if (taskPo == null) {
                log.warn("音频合成任务不存在: taskId={}", taskId);
                return;
            }

            // 更新任务状态
            LambdaUpdateWrapper<AiAudioSynthesisTaskPo> updateWrapper = new LambdaUpdateWrapper<AiAudioSynthesisTaskPo>()
                    .eq(AiAudioSynthesisTaskPo::getTaskId, taskId)
                    .set(AiAudioSynthesisTaskPo::getStatus, success ?
                        AiAudioSynthesisTaskPo.Status.COMPLETED : AiAudioSynthesisTaskPo.Status.FAILED)
                    .set(AiAudioSynthesisTaskPo::getProcessingEndTime, new Date())
                    .set(AiAudioSynthesisTaskPo::getUpdateTime, new Date());

            if (success && resultAudioUrl != null) {
                updateWrapper.set(AiAudioSynthesisTaskPo::getResultAudioUrl, resultAudioUrl);
            }

            if (!success && errorMessage != null) {
                updateWrapper.set(AiAudioSynthesisTaskPo::getErrorMessage, errorMessage);
            }

            if (taskCostTime != null) {
                updateWrapper.set(AiAudioSynthesisTaskPo::getTaskCostTime, taskCostTime);
            }

            int updateCount = audioSynthesisTaskMapper.update(null, updateWrapper);

            if (updateCount == 0) {
                log.warn("音频合成任务回调处理失败，任务不存在: taskId={}", taskId);
                return;
            }

            log.info("音频合成任务回调处理成功: taskId={}, success={}", taskId, success);

            // 如果音频合成成功，先上传到OSS，然后触发对口型处理
            if (success && resultAudioUrl != null) {
                try {
                    // 上传音频到OSS
                    String ossAudioUrl = uploadAudioToOss(resultAudioUrl, taskPo.getUserId());

                    // 更新任务记录中的OSS URL
                    audioSynthesisTaskMapper.update(null,
                            new LambdaUpdateWrapper<AiAudioSynthesisTaskPo>()
                                    .eq(AiAudioSynthesisTaskPo::getTaskId, taskId)
                                    .set(AiAudioSynthesisTaskPo::getResultAudioUrl, ossAudioUrl)
                                    .set(AiAudioSynthesisTaskPo::getUpdateTime, new Date())
                    );

                    log.info("音频已上传到OSS: taskId={}, originalUrl={}, ossUrl={}",
                            taskId, resultAudioUrl, ossAudioUrl);

                    // 使用OSS URL触发对口型处理
                    triggerLipSyncAfterAudioSynthesis(taskPo.getShotId(), ossAudioUrl,taskPo.getUserId());

                } catch (Exception e) {
                    log.error("上传音频到OSS失败: taskId={}, audioUrl={}", taskId, resultAudioUrl, e);

                    // 更新任务状态为失败
                    audioSynthesisTaskMapper.update(null,
                            new LambdaUpdateWrapper<AiAudioSynthesisTaskPo>()
                                    .eq(AiAudioSynthesisTaskPo::getTaskId, taskId)
                                    .set(AiAudioSynthesisTaskPo::getStatus, AiAudioSynthesisTaskPo.Status.FAILED)
                                    .set(AiAudioSynthesisTaskPo::getErrorMessage, "上传音频到OSS失败: " + e.getMessage())
                                    .set(AiAudioSynthesisTaskPo::getUpdateTime, new Date())
                    );

                    // 更新分镜状态为失败
                    updateShotStatusToFailed(taskPo.getShotId(), "上传音频到OSS失败: " + e.getMessage());
                }
            } else if (!success) {
                // 音频合成失败，更新分镜状态为失败
                updateShotStatusToFailed(taskPo.getShotId(), errorMessage != null ? errorMessage : "音频合成失败");
            }

        } catch (Exception e) {
            log.error("处理音频合成回调异常: taskId={}", taskId, e);
        }
    }

    @Override
    public void triggerLipSyncAfterAudioSynthesis(Long shotId, String resultAudioUrl, String userId) {
        log.info("音频合成完成，触发对口型处理: shotId={}, audioUrl={}", shotId, resultAudioUrl);

        try {
            // 调用对口型服务的方法来开始对口型处理
            shotLipSyncService.startLipSyncAfterAudioSynthesis(shotId, resultAudioUrl, userId);
        } catch (Exception e) {
            log.error("触发对口型处理失败: shotId={}, audioUrl={}", shotId, resultAudioUrl, e);
        }
    }

    /**
     * 构建ComfyUI节点信息列表
     */
    private List<ComfyUINodeInfo> buildNodeInfoList(List<String> audioUrls) {
        List<ComfyUINodeInfo> nodeInfoList = new ArrayList<>();

        // 根据您提供的示例，节点ID从19到25，最多7个音频
        String[] nodeIds = {"19", "20", "21", "22", "23", "24", "25"};
        String[] descriptions = {"audio-1", "audio-2", "audio-3", "audio-4", "audio-5", "audio-6", "audio-7"};

        for (int i = 0; i < nodeIds.length; i++) {
            ComfyUINodeInfo nodeInfo = new ComfyUINodeInfo();
            nodeInfo.setNodeId(nodeIds[i]);
            nodeInfo.setFieldName("value");
            
            if (i < audioUrls.size()) {
                // 有音频URL，使用实际URL
                nodeInfo.setFieldValue(MediaUrlPrefixUtil.getMediaUrl(audioUrls.get(i)));
            } else {
                // 没有音频URL，使用空字符串
                nodeInfo.setFieldValue("");
            }
            
            nodeInfoList.add(nodeInfo);
        }

        return nodeInfoList;
    }

    /**
     * 创建任务记录
     */
    private AiAudioSynthesisTaskPo createTaskRecord(AudioSynthesisRequest request, ComfyUIRunResponse response) {
        AiAudioSynthesisTaskPo taskPo = new AiAudioSynthesisTaskPo();
        taskPo.setTaskId(response.getData().getTaskId());
        taskPo.setShotId(request.getShotId());
        taskPo.setUserId(request.getUserId());
        taskPo.setAudioUrls(JSON.toJSONString(request.getAudioUrls()));
        taskPo.setAudioCount(request.getAudioUrls().size());
        taskPo.setWebappId(request.getWebappId());
        taskPo.setApiKey(request.getApiKey());
        taskPo.setClientId(response.getData().getClientId());
        taskPo.setNetWssUrl(response.getData().getNetWssUrl());
        taskPo.setStatus(AiAudioSynthesisTaskPo.Status.PROCESSING);
        taskPo.setRequestParams(JSON.toJSONString(request));
        taskPo.setProcessingStartTime(new Date());
        taskPo.setCreateTime(new Date());
        taskPo.setUpdateTime(new Date());
        taskPo.setDelFlag(0);

        int insertResult = audioSynthesisTaskMapper.insert(taskPo);
        if (insertResult <= 0) {
            throw new BizException("创建音频合成任务记录失败");
        }

        log.info("创建音频合成任务记录成功: taskId={}, shotId={}", taskPo.getTaskId(), taskPo.getShotId());
        return taskPo;
    }

    /**
     * 转换任务记录为结果对象
     */
    private AudioSynthesisResult convertToResult(AiAudioSynthesisTaskPo taskPo) {
        AudioSynthesisResult result = new AudioSynthesisResult();
        result.setTaskId(taskPo.getTaskId());
        result.setShotId(taskPo.getShotId());
        result.setStatus(taskPo.getStatus());
        result.setResultAudioUrl(taskPo.getResultAudioUrl());
        result.setErrorMessage(taskPo.getErrorMessage());
        result.setTaskCostTime(taskPo.getTaskCostTime());
        result.setNetWssUrl(taskPo.getNetWssUrl());
        result.setClientId(taskPo.getClientId());

        // 设置成功状态
        if (AiAudioSynthesisTaskPo.Status.COMPLETED.equals(taskPo.getStatus())) {
            result.setSuccess(true);
        } else if (AiAudioSynthesisTaskPo.Status.FAILED.equals(taskPo.getStatus()) || 
                   AiAudioSynthesisTaskPo.Status.CANCELLED.equals(taskPo.getStatus())) {
            result.setSuccess(false);
        } else {
            result.setSuccess(null); // 处理中状态
        }

        // 设置时间
        if (taskPo.getProcessingStartTime() != null) {
            result.setStartTime(taskPo.getProcessingStartTime().getTime());
        }
        if (taskPo.getProcessingEndTime() != null) {
            result.setEndTime(taskPo.getProcessingEndTime().getTime());
        }

        return result;
    }

    /**
     * 上传音频文件到OSS
     *
     * @param audioUrl 原始音频URL
     * @param userId 用户ID
     * @return OSS音频URL
     */
    private String uploadAudioToOss(String audioUrl, String userId) {
        try {
            log.info("开始上传音频到OSS: audioUrl={}, userId={}", audioUrl, userId);

            // 确定文件扩展名
            String fileExtension = getAudioFileExtension(audioUrl);

            // 构建OSS路径
            String ossPath = OSS_AUDIO_PATH.replace("{env}", env)
                    .replace("{userId}", userId)
                    .replace("{type}", "audio-synthesis");

            // 生成唯一文件名
            String fileName = "synthesis_" + IdUtil.fastSimpleUUID() + "." + fileExtension;
            String fullOssPath = ossPath + fileName;

            // 调用OSS工具类上传文件
            String ossObjectName = ossUtils.uploadFile(audioUrl, fullOssPath);

            log.info("音频上传到OSS成功: originalUrl={}, ossUrl={}", audioUrl, ossObjectName);
            return ossObjectName;

        } catch (Exception e) {
            log.error("上传音频到OSS失败: audioUrl={}, userId={}", audioUrl, userId, e);
            throw new BizException("上传音频到OSS失败: " + e.getMessage());
        }
    }

    /**
     * 获取音频文件扩展名
     *
     * @param audioUrl 音频URL
     * @return 文件扩展名
     */
    private String getAudioFileExtension(String audioUrl) {
        if (audioUrl == null || audioUrl.isEmpty()) {
            return "wav"; // 默认扩展名
        }

        // 从URL中提取文件扩展名
        String lowerUrl = audioUrl.toLowerCase();
        if (lowerUrl.contains(".mp3")) {
            return "mp3";
        } else if (lowerUrl.contains(".wav")) {
            return "wav";
        } else if (lowerUrl.contains(".ogg")) {
            return "ogg";
        } else if (lowerUrl.contains(".m4a")) {
            return "m4a";
        } else if (lowerUrl.contains(".flac")) {
            return "flac";
        } else if (lowerUrl.contains(".aac")) {
            return "aac";
        } else {
            return "wav"; // 默认扩展名
        }
    }

    /**
     * 更新分镜状态为失败
     *
     * @param shotId 分镜ID
     * @param errorMessage 错误信息
     */
    private void updateShotStatusToFailed(Long shotId, String errorMessage) {
        try {
            log.info("更新分镜状态为失败: shotId={}, errorMessage={}", shotId, errorMessage);

            // 获取分镜对象并更新
            AiCanvasShotPo shotPo = aiCanvasShotService.getById(shotId);
            if (shotPo == null) {
                log.warn("分镜不存在: shotId={}", shotId);
                return;
            }

            shotPo.setShotStatus(ShotStatus.FAILED.getValue());
            shotPo.setUpdateTime(new Date());

            boolean updateResult = aiCanvasShotService.updateById(shotPo);
            if (!updateResult) {
                log.error("更新分镜状态为失败失败: shotId={}", shotId);
            } else {
                log.info("分镜状态更新为失败成功: shotId={}", shotId);
            }

        } catch (Exception e) {
            log.error("更新分镜状态为失败异常: shotId={}, error={}", shotId, e.getMessage(), e);
        }
    }
}
