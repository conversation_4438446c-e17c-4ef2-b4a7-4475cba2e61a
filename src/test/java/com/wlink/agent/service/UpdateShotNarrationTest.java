package com.wlink.agent.service;

import com.alibaba.fastjson.JSON;
import com.wlink.agent.dao.mapper.AiCreationSessionMapper;
import com.wlink.agent.dao.mapper.AiShotMapper;
import com.wlink.agent.dao.mapper.AiTtsRecordMapper;
import com.wlink.agent.dao.po.AiCreationSessionPo;
import com.wlink.agent.dao.po.AiShotPo;
import com.wlink.agent.dao.po.AiTtsRecordPo;
import com.wlink.agent.model.req.ShotSaveReq;
import com.wlink.agent.model.req.UpdateShotNarrationReq;
import com.wlink.agent.model.res.TtsGenerateRes;
import com.wlink.agent.model.res.UpdateShotNarrationRes;
import com.wlink.agent.service.impl.AiShotServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 修改分镜旁白功能测试
 */
@ExtendWith(MockitoExtension.class)
class UpdateShotNarrationTest {

    @Mock
    private AiShotMapper aiShotMapper;

    @Mock
    private AiTtsRecordMapper aiTtsRecordMapper;

    @Mock
    private AiCreationSessionMapper aiCreationSessionMapper;

    @Mock
    private AiCreationContentService aiCreationContentService;

    @InjectMocks
    private AiShotServiceImpl aiShotService;

    private UpdateShotNarrationReq request;
    private AiShotPo shotPo;
    private ShotSaveReq.ShotGroupsDTO.ShotsDTO shotData;

    @BeforeEach
    void setUp() {
        // 准备测试请求
        request = new UpdateShotNarrationReq();
        request.setShotId(123L);
        request.setNarrationText("这是修改后的旁白内容");
        request.setIndex(1);

        // 准备分镜数据
        shotPo = new AiShotPo();
        shotPo.setId(123L);
        shotPo.setSessionId("session-123");
        shotPo.setShotId("shot-123");

        // 准备shot_data JSON数据
        shotData = new ShotSaveReq.ShotGroupsDTO.ShotsDTO();
        List<ShotSaveReq.ShotLines> lineList = new ArrayList<>();
        
        ShotSaveReq.ShotLines line1 = new ShotSaveReq.ShotLines();
        line1.setId(1);
        line1.setName("旁白");
        line1.setLine("原始旁白内容");
        lineList.add(line1);

        ShotSaveReq.ShotLines line2 = new ShotSaveReq.ShotLines();
        line2.setId(2);
        line2.setName("角色A");
        line2.setLine("角色对话内容");
        lineList.add(line2);

        shotData.setLineList(lineList);
        shotPo.setShotData(JSON.toJSONString(shotData));
    }

    @Test
    void testUpdateShotNarration_Success() {
        // Mock数据库查询
        when(aiShotMapper.selectById(123L)).thenReturn(shotPo);

        // Mock TTS记录查询
        AiTtsRecordPo ttsRecord = new AiTtsRecordPo();
        ttsRecord.setVoiceId(456L);
        ttsRecord.setRate("1.0");
        ttsRecord.setPitch(0);
        ttsRecord.setVolume("1.0");
        when(aiTtsRecordMapper.selectOne(any())).thenReturn(ttsRecord);

        // Mock TTS生成服务
        TtsGenerateRes ttsRes = new TtsGenerateRes();
        ttsRes.setAudioUrl("/data/audio/new_narration.wav");
        ttsRes.setDuration(5000L);
        ttsRes.setRecordId(789L);
        when(aiCreationContentService.generateTts(any())).thenReturn(ttsRes);

        // Mock数据库更新
        when(aiShotMapper.updateById(any(AiShotPo.class))).thenReturn(1);

        // 执行测试
        UpdateShotNarrationRes result = aiShotService.updateShotNarration(request);

        // 验证结果
        assertNotNull(result);
        assertEquals(123L, result.getShotId());
        assertEquals("这是修改后的旁白内容", result.getNarrationText());
        assertEquals("/data/audio/new_narration.wav", result.getAudioUrl());
        assertEquals(5000L, result.getDuration());
        assertEquals(789L, result.getRecordId());

        // 验证方法调用
        verify(aiShotMapper).selectById(123L);
        verify(aiTtsRecordMapper).selectOne(any());
        verify(aiCreationContentService).generateTts(any());
        verify(aiShotMapper).updateById(any(AiShotPo.class));
    }

    @Test
    void testUpdateShotNarration_ShotNotFound() {
        // Mock分镜不存在
        when(aiShotMapper.selectById(123L)).thenReturn(null);

        // 执行测试并验证异常
        assertThrows(Exception.class, () -> {
            aiShotService.updateShotNarration(request);
        });

        verify(aiShotMapper).selectById(123L);
        verifyNoInteractions(aiTtsRecordMapper);
        verifyNoInteractions(aiCreationContentService);
    }

    @Test
    void testUpdateShotNarration_IndexNotFound() {
        // 设置不存在的索引
        request.setIndex(999);

        when(aiShotMapper.selectById(123L)).thenReturn(shotPo);

        // 执行测试并验证异常
        assertThrows(Exception.class, () -> {
            aiShotService.updateShotNarration(request);
        });

        verify(aiShotMapper).selectById(123L);
        verifyNoInteractions(aiTtsRecordMapper);
        verifyNoInteractions(aiCreationContentService);
    }

    @Test
    void testUpdateShotNarration_WithSessionDefaultSound() {
        // Mock没有TTS记录，使用会话默认音色
        when(aiShotMapper.selectById(123L)).thenReturn(shotPo);
        when(aiTtsRecordMapper.selectOne(any())).thenReturn(null);

        // Mock会话信息
        AiCreationSessionPo sessionPo = new AiCreationSessionPo();
        sessionPo.setSessionId("session-123");
        sessionPo.setSoundId(999L);
        when(aiCreationSessionMapper.selectOne(any())).thenReturn(sessionPo);

        // Mock TTS生成服务
        TtsGenerateRes ttsRes = new TtsGenerateRes();
        ttsRes.setAudioUrl("/data/audio/default_narration.wav");
        ttsRes.setDuration(3000L);
        ttsRes.setRecordId(888L);
        when(aiCreationContentService.generateTts(any())).thenReturn(ttsRes);

        when(aiShotMapper.updateById(any(AiShotPo.class))).thenReturn(1);

        // 执行测试
        UpdateShotNarrationRes result = aiShotService.updateShotNarration(request);

        // 验证结果
        assertNotNull(result);
        assertEquals("/data/audio/default_narration.wav", result.getAudioUrl());

        verify(aiCreationSessionMapper).selectOne(any());
        verify(aiCreationContentService).generateTts(any());
    }
}
