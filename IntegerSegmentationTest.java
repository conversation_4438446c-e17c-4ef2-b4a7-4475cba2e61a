public class IntegerSegmentationTest {
    
    public static void main(String[] args) {
        // Test different durations
        double[] testDurations = {0.3, 0.5, 1.0, 2.5, 5.0, 10.01, 15.7, 30.0, 60.5, 120.3};
        
        System.out.println("Testing integer-only audio segmentation algorithm");
        System.out.println("=".repeat(80));
        
        for (double duration : testDurations) {
            double segmentDuration = calculateAudioSegmentation(duration);
            
            // Verify the result
            String verification = verifySegmentation(duration, segmentDuration);
            boolean isInteger = segmentDuration == Math.floor(segmentDuration);
            
            System.out.printf("Duration: %6.2fs -> Segment: %4.0fs [%s] %s%n", 
                    duration, segmentDuration, verification, isInteger ? "✓INT" : "✗DEC");
        }
    }
    
    private static double calculateAudioSegmentation(double totalDurationSeconds) {
        if (totalDurationSeconds <= 0.4) {
            return 1.0;
        }

        // Try preferred integer durations (from large to small)
        int[] preferredDurations = {10, 8, 6, 5, 4, 3, 2, 1};
        
        for (int segmentDuration : preferredDurations) {
            if (segmentDuration > totalDurationSeconds) {
                continue;
            }
            
            int fullSegments = (int) Math.floor(totalDurationSeconds / segmentDuration);
            double remainder = totalDurationSeconds - (fullSegments * segmentDuration);
            
            if (remainder < 0.001 || remainder >= 0.4) {
                return (double) segmentDuration;
            }
        }

        // If no preferred duration works, find optimal integer segment duration
        for (int segmentDuration = 1; segmentDuration <= (int) totalDurationSeconds; segmentDuration++) {
            int fullSegments = (int) Math.floor(totalDurationSeconds / segmentDuration);
            double remainder = totalDurationSeconds - (fullSegments * segmentDuration);
            
            if (remainder < 0.001 || remainder >= 0.4) {
                return (double) segmentDuration;
            }
        }

        // Fallback: return integer part of total duration
        return (double) Math.max(1, (int) Math.floor(totalDurationSeconds));
    }
    
    private static String verifySegmentation(double totalDuration, double segmentDuration) {
        if (totalDuration <= 0.4) {
            return "OK (Short audio)";
        }
        
        int fullSegments = (int) Math.floor(totalDuration / segmentDuration);
        double lastSegmentDuration = totalDuration - (fullSegments * segmentDuration);
        
        if (Math.abs(lastSegmentDuration) < 0.001) {
            return String.format("OK (Perfect: %d×%ds)", fullSegments, (int)segmentDuration);
        } else if (lastSegmentDuration >= 0.4) {
            return String.format("OK (%d×%ds + %.2fs)", fullSegments, (int)segmentDuration, lastSegmentDuration);
        } else {
            return String.format("FAIL (Last: %.2fs < 0.4s)", lastSegmentDuration);
        }
    }
}
