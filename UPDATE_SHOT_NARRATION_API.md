# 修改分镜旁白接口文档

## 接口概述

该接口用于修改指定分镜中的旁白内容，并自动重新生成对应的音频文件。

## 接口信息

- **URL**: `/agent/ai-shot/narration/update`
- **方法**: `PUT`
- **Content-Type**: `application/json`

## 请求参数

### 请求体 (JSON)

```json
{
  "shotId": 123,
  "narrationText": "这是新的旁白内容",
  "index": 1
}
```

### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| shotId | Long | 是 | 分镜ID，对应ai_shot表的主键ID |
| narrationText | String | 是 | 新的旁白文本内容 |
| index | Integer | 是 | 旁白在line_list中的索引ID |

## 响应结果

### 成功响应

```json
{
  "success": true,
  "data": {
    "shotId": 123,
    "narrationText": "这是新的旁白内容",
    "audioUrl": "/data/audio/narration_20240804110000_abcdef12.wav",
    "duration": 5000,
    "recordId": 789
  },
  "errorCode": null,
  "errorMessage": null
}
```

### 响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| shotId | Long | 分镜ID |
| narrationText | String | 更新后的旁白文本 |
| audioUrl | String | 生成的音频文件URL |
| duration | Long | 音频时长（毫秒） |
| recordId | Long | TTS记录ID |

### 错误响应

```json
{
  "success": false,
  "data": null,
  "errorCode": "PARAM_ERROR",
  "errorMessage": "分镜不存在"
}
```

## 业务逻辑说明

1. **参数校验**: 验证shotId、narrationText、index是否为空
2. **分镜查询**: 根据shotId查询ai_shot表获取分镜数据
3. **数据解析**: 解析shot_data字段的JSON数据，获取line_list
4. **索引匹配**: 根据index在line_list中找到对应的旁白记录
5. **音频参数获取**: 查询ai_tts_record表获取音频生成参数（音色、语速等）
6. **音频生成**: 调用TTS服务生成新的音频文件
7. **数据更新**: 更新shot_data并保存到数据库

## 使用示例

### cURL 示例

```bash
curl -X PUT "http://localhost:8080/agent/ai-shot/narration/update" \
  -H "Content-Type: application/json" \
  -d '{
    "shotId": 123,
    "narrationText": "这是修改后的旁白内容",
    "index": 1
  }'
```

### JavaScript 示例

```javascript
const response = await fetch('/agent/ai-shot/narration/update', {
  method: 'PUT',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    shotId: 123,
    narrationText: '这是修改后的旁白内容',
    index: 1
  })
});

const result = await response.json();
console.log(result);
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| PARAM_ERROR | 参数错误（分镜ID为空、旁白文本为空、索引为空、分镜不存在、索引不存在等） |
| SYSTEM_ERROR | 系统错误（数据库操作失败、JSON解析失败等） |
| SOUND_NOT_EXISTS | 音色不存在 |

## 注意事项

1. 该接口会重新生成音频，可能需要一定的处理时间
2. 如果没有找到对应的TTS记录，会使用会话的默认音色
3. 音频生成失败不会影响旁白文本的更新
4. 索引必须在现有的line_list中存在，否则会返回错误
5. 生成的音频文件会自动上传到OSS存储
